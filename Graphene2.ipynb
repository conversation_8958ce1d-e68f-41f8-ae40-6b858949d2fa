import os
import pandas as pd
import numpy as np
from arpes.load_pxt import read_single_pxt
import tkinter as tk
from tkinter import filedialog, messagebox
import os
import pandas as pd
import numpy as np
from arpes.load_pxt import read_single_pxt
import tkinter as tk
from tkinter import filedialog, messagebox
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

igor_data = np.array([
    [57600, 54784, 58112],
    [56561.95, 53415.66, 57121.13],
    [55523.89, 52047.31, 56130.26],
    [54485.84, 50678.96, 55139.39],
    [53447.78, 49310.62, 54148.52],
    [52409.73, 47942.27, 53157.65],
    [51371.67, 46655.25, 52193.88],
    [50333.62, 45428.45, 51250.2],
    [49295.56, 44201.66, 50306.51],
    [48257.51, 42974.87, 49362.82],
    [47219.45, 41748.08, 48419.14],
    [46223.56, 40563.45, 47510.59],
    [45468.61, 39619.77, 46802.82],
    [44713.66, 38676.08, 46095.06],
    [43958.71, 37732.39, 45387.29],
    [43203.77, 36788.71, 44679.53],
    [42448.82, 35845.02, 43971.77],
    [41693.87, 34833.07, 43195.73],
    [40938.92, 33795.01, 42393.6],
    [40183.97, 32756.96, 41591.46],
    [39429.02, 31718.9, 40789.33],
    [38674.07, 30680.85, 39987.2],
    [37919.12, 29670.9, 39171.01],
    [37164.17, 28727.21, 38321.7],
    [36409.22, 27783.53, 37472.38],
    [35654.27, 26839.84, 36623.06],
    [34899.32, 25896.16, 35773.74],
    [34144.38, 24952.47, 34924.42],
    [33512.91, 24173.43, 34239.75],
    [32899.52, 23418.48, 33579.17],
    [32286.12, 22663.53, 32918.59],
    [31672.72, 21908.58, 32258.01],
    [31059.33, 21153.63, 31597.43],
    [30467.01, 20419.77, 30957.93],
    [29900.8, 19712, 30344.53],
    [29334.59, 19004.23, 29731.14],
    [28768.38, 18296.47, 29117.74],
    [28202.16, 17588.71, 28504.35],
    [27641.98, 16886.96, 27901.99],
    [27358.87, 16462.31, 27807.62],
    [27075.77, 16037.65, 27713.26],
    [26792.66, 15612.99, 27618.89],
    [26509.55, 15188.33, 27524.52],
    [26226.45, 14763.67, 27430.15],
    [26027.67, 14479.56, 27448.22],
    [25886.12, 14290.82, 27542.59],
    [25744.56, 14102.09, 27636.96],
    [25603.01, 13913.35, 27731.33],
    [25461.46, 13724.61, 27825.69],
    [25279.75, 13503.75, 27944.16],
    [24902.28, 13126.27, 28180.08],
    [24524.8, 12748.8, 28416],
    [24147.33, 12371.33, 28651.92],
    [23769.85, 11993.85, 28887.84],
    [23392.38, 11616.38, 29123.77],
    [22874.35, 11168.63, 29359.69],
    [22308.14, 10696.78, 29595.61],
    [21741.93, 10224.94, 29831.53],
    [21175.72, 9753.098, 30067.45],
    [20609.51, 9281.255, 30303.37],
    [19952.94, 8899.765, 30539.29],
    [19103.62, 8711.027, 30775.21],
    [18254.31, 8522.29, 31011.14],
    [17404.99, 8333.553, 31247.06],
    [16555.67, 8144.816, 31482.98],
    [15706.35, 7956.079, 31718.9],
    [14688.38, 7893.835, 31828.33],
    [13650.32, 7846.651, 31922.7],
    [12612.27, 7799.467, 32017.07],
    [11574.21, 7752.282, 32111.44],
    [10536.16, 7705.098, 32205.8],
    [9807.31, 7922.949, 32388.52],
    [9429.835, 8441.977, 32671.62],
    [9052.36, 8961.004, 32954.73],
    [8674.887, 9480.031, 33237.84],
    [8297.412, 9999.059, 33520.94],
    [7911.906, 10526.12, 33812.08],
    [7345.694, 11233.88, 34283.92],
    [6779.482, 11941.65, 34755.77],
    [6213.271, 12649.41, 35227.61],
    [5647.059, 13357.18, 35699.45],
    [5080.847, 14064.94, 36171.29],
    [4543.749, 14714.48, 36614.02],
    [4024.722, 15327.87, 37038.68],
    [3505.694, 15941.27, 37463.34],
    [2986.667, 16554.67, 37888],
    [2467.639, 17168.06, 38312.66],
    [1984.753, 17790.49, 38764.42],
    [1654.463, 18451.07, 39330.64],
    [1324.173, 19111.65, 39896.85],
    [993.8823, 19772.23, 40463.06],
    [663.5922, 20432.82, 41029.27],
    [333.302, 21093.4, 41595.48],
    [256, 21464.85, 41944.85],
    [256, 21747.95, 42227.95],
    [256, 22031.06, 42511.06],
    [256, 22314.16, 42794.16],
    [256, 22597.27, 43077.27],
    [239.9373, 23008.88, 43456.75],
    [192.7529, 23669.46, 44022.96],
    [145.5686, 24330.04, 44589.18],
    [98.38432, 24990.62, 45155.39],
    [51.2, 25651.2, 45721.6],
    [4.015687, 26311.78, 46287.81],
    [0, 26972.36, 46897.19],
    [0, 27632.94, 47510.59],
    [0, 28293.52, 48123.98],
    [0, 28954.1, 48737.38],
    [0, 29614.68, 49350.78],
    [0, 30344.53, 50033.44],
    [0, 31146.67, 50788.39],
    [0, 31948.8, 51543.34],
    [0, 32750.93, 52298.29],
    [0, 33553.07, 53053.24],
    [0, 34358.21, 53805.18],
    [0, 35207.53, 54512.94],
    [0, 36056.85, 55220.71],
    [0, 36906.16, 55928.47],
    [0, 37755.48, 56636.23],
    [0, 38604.8, 57344],
    [0, 39062.59, 57208.47],
    [0, 39298.51, 56595.07],
    [0, 39534.43, 55981.68],
    [0, 39770.35, 55368.28],
    [0, 40006.27, 54754.89],
    [0, 40181.96, 54041.1],
    [0, 40134.78, 52955.86],
    [0, 40087.59, 51870.62],
    [0, 40040.41, 50785.38],
    [0, 39993.22, 49700.14],
    [0, 39946.04, 48614.9],
    [0, 39936, 47641.1],
    [0, 39936, 46697.41],
    [0, 39936, 45753.73],
    [0, 39936, 44810.04],
    [0, 39936, 43866.35],
    [0, 39918.93, 42854.4],
    [0, 39871.75, 41721.98],
    [0, 39824.57, 40589.55],
    [0, 39777.38, 39457.13],
    [0, 39730.2, 38324.71],
    [0, 39683.01, 37192.28],
    [0, 39680, 36369.07],
    [0, 39680, 35566.93],
    [0, 39680, 34764.8],
    [0, 39680, 33962.67],
    [0, 39680, 33160.54],
    [0, 39680, 32527.06],
    [0, 39680, 32055.21],
    [0, 39680, 31583.37],
    [0, 39680, 31111.53],
    [0, 39680, 30639.69],
    [0, 39675.98, 30123.67],
    [0, 39628.8, 29132.8],
    [0, 39581.62, 28141.93],
    [0, 39534.43, 27151.06],
    [0, 39487.25, 26160.19],
    [0, 39440.06, 25169.32],
    [0, 39361.76, 24240.69],
    [0, 39267.39, 23344.19],
    [0, 39173.02, 22447.69],
    [0, 39078.65, 21551.18],
    [0, 38984.28, 20654.68],
    [0, 38923.04, 19835.48],
    [0, 38970.23, 19269.27],
    [0, 39017.41, 18703.06],
    [0, 39064.6, 18136.85],
    [0, 39111.78, 17570.63],
    [0, 39158.96, 17004.42],
    [0, 39435.04, 16781.55],
    [0, 39765.33, 16640],
    [0, 40095.62, 16498.45],
    [0, 40425.91, 16356.89],
    [0, 40756.2, 16215.34],
    [993.8823, 41122.64, 16073.79],
    [3589.02, 41547.29, 15932.24],
    [6184.157, 41971.95, 15790.68],
    [8779.294, 42396.61, 15649.13],
    [11374.43, 42821.27, 15507.58],
    [13969.57, 43245.93, 15366.02],
    [15796.71, 43715.77, 15224.47],
    [17589.71, 44187.61, 15082.92],
    [19382.71, 44659.45, 14941.36],
    [21175.72, 45131.29, 14799.81],
    [22968.72, 45603.14, 14658.26],
    [24686.43, 46100.08, 14516.71],
    [26337.88, 46619.11, 14375.15],
    [27989.33, 47138.13, 14233.6],
    [29640.79, 47657.16, 14092.05],
    [31292.23, 48176.19, 13950.49],
    [32933.65, 48705.25, 13798.9],
    [34490.73, 49318.65, 13562.98],
    [36047.81, 49932.05, 13327.06],
    [37604.89, 50545.44, 13091.14],
    [39161.98, 51158.84, 12855.22],
    [40719.06, 51772.23, 12619.29],
    [41922.76, 52225, 12415.5],
    [42960.82, 52602.48, 12226.76],
    [43998.87, 52979.95, 12038.02],
    [45036.93, 53357.43, 11849.29],
    [46074.98, 53734.9, 11660.55],
    [47293.74, 54196.71, 11411.58],
    [49039.56, 54904.47, 10986.92],
    [50785.38, 55612.23, 10562.26],
    [52531.2, 56320, 10137.6],
    [54277.02, 57027.77, 9712.941],
    [56022.84, 57735.53, 9288.282],
    [57494.59, 58325.84, 8785.317],
    [58910.12, 58892.05, 8266.29],
    [60325.65, 59458.26, 7747.263],
    [61741.18, 60024.47, 7228.235],
    [63156.71, 60590.68, 6709.208],
    [64076.3, 60470.21, 6457.224],
    [64265.04, 59337.79, 6598.776],
    [64453.77, 58205.36, 6740.33],
    [64642.51, 57072.94, 6881.882],
    [64831.25, 55940.52, 7023.435],
    [65019.98, 54808.09, 7164.988],
    [64746.92, 53260.05, 7398.902],
    [64463.81, 51702.96, 7634.824],
    [64180.71, 50145.88, 7870.745],
    [63897.6, 48588.8, 8106.667],
    [63614.49, 47031.72, 8342.588],
    [63592.41, 45605.14, 8474.102],
    [63781.14, 44283.98, 8521.286],
    [63969.88, 42962.82, 8568.471],
    [64158.62, 41641.66, 8615.655],
    [64347.36, 40320.5, 8662.839],
    [64415.62, 38993.32, 8704],
    [63660.68, 37624.97, 8704],
    [62905.73, 36256.63, 8704],
    [62150.78, 34888.28, 8704],
    [61395.83, 33519.94, 8704],
    [60640.88, 32151.59, 8704],
    [60283.48, 30882.63, 8704],
    [60094.75, 29655.84, 8704],
    [59906.01, 28429.05, 8704],
    [59717.27, 27202.26, 8704],
    [59528.54, 25975.47, 8704],
    [59339.8, 24722.57, 8704],
    [59151.06, 23401.41, 8704],
    [58962.32, 22080.25, 8704],
    [58773.59, 20759.09, 8704],
    [58584.85, 19437.93, 8704],
    [58396.11, 18116.77, 8704],
    [58287.69, 17197.18, 8704],
    [58193.32, 16347.86, 8704],
    [58098.95, 15498.54, 8704],
    [58004.58, 14649.22, 8704],
    [57910.21, 13799.91, 8704],
    [57795.77, 12267.92, 8704],
    [57654.21, 9814.337, 8704],
    [57512.66, 7360.753, 8704],
    [57371.11, 4907.168, 8704],
    [57229.55, 2453.584, 8704],
    [57088, 0, 8704]])
# Create the array from the provided data


# Normalize the RGB values to the range 0-1
normalized_data = igor_data / 65535.0

# Create the ListedColormap
rainbowlightct = ListedColormap(normalized_data)
rainbowlightctplotly = [f'rgb({int(r*255)},{int(g*255)},{int(b*255)})' for r,g,b in normalized_data]
warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


def load_pxt_files():
    folder_path = filedialog.askdirectory(title="Select folder containing PXT files")
    if not folder_path:
        return

    pxt_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    if not pxt_files:
        messagebox.showinfo("No PXT files", "No PXT files found in the selected folder.")
        return

    pxt_files.sort()
    data_arrays = []
    attributes = []
    
    for file in pxt_files:
        file_path = os.path.join(folder_path, file)
        try:
            data = read_single_pxt(file_path)
            df = pd.DataFrame(data.values, 
                            columns=data.coords['phi'].values,
                            index=data.coords['eV'].values)
            data_arrays.append(df)
            attributes.append(data.attrs)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {file}: {str(e)}")

    messagebox.showinfo("Success", f"Loaded {len(data_arrays)} PXT files.")
    return data_arrays, attributes

# GUI setup
root = tk.Tk()
root.title("ARPES Data Processor")

def load_and_process():
    global data_proc, data_EK, data_attributes
    data_raw, data_attributes = load_pxt_files()
    
    # Clear previous data
    
    # Convert to binding energy
    work_function = 4.5  # eV
    data_proc = []
    data_EK = []
    for i, df in enumerate(data_raw):
        hv = data_attributes[i]['hv']
        new_index = hv - work_function - df.index.values
        proc_df = df.set_index(pd.Index(new_index))
        data_proc.append(proc_df)
    
    # Convert to E-kx-ky space
    for i, df in enumerate(data_proc):
        hv = data_attributes[i]['hv']
        polar = np.deg2rad(data_attributes[i]['polar'])
        
        # Get dimensions
        n_energy = len(df.index)
        n_angles = len(df.columns)
        
        # Create meshgrids for proper broadcasting
        E_binding = df.index.values[:, np.newaxis]  # Shape (n_energy, 1)
        theta = np.deg2rad(df.columns.values)        # Shape (n_angles,)
        
        # Broadcast to 2D grids
        E_binding_grid = np.tile(E_binding, (1, n_angles))  # Shape (n_energy, n_angles)
        theta_grid = np.tile(theta, (n_energy, 1))          # Shape (n_energy, n_angles)
        
        # Calculate momentum components
        E_kinetic = hv - work_function - E_binding_grid
        k_mag = 0.5123 * np.sqrt(E_binding_grid)
        
        kx = k_mag * np.sin(theta_grid)
        ky = k_mag * np.sin(polar)
        
        # Create DataFrame with equal-length arrays
        scan_data = {
            'E_binding': -E_kinetic.flatten(),
            'kx': kx.flatten(),
            'ky': ky.flatten(),
            'intensity': df.values.flatten()
        }
        
        data_EK.append(pd.DataFrame(scan_data))
        
load_button = tk.Button(root, text="Load and Process Data", command=load_and_process)
load_button.pack(pady=20)
root.mainloop()


import pandas as pd
import numpy as np
import plotly.express as px

def process_threshold_and_filter(data, threshold, window_size=3, 
                                min_periods=1, suppress_at='max'):
    # Convert input data to DataFrame
    if isinstance(data, (list, np.ndarray)):
        data_arr = np.asarray(data)
        df = pd.DataFrame(data_arr.reshape(-1, data_arr.shape[-1]),
                         columns=['binding_energy', 'kx', 'ky', 'intensity'])
    else:
        df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])

    # Clean data if stored as lists
    if df.applymap(lambda x: isinstance(x, list)).any().any():
        df = df.applymap(lambda y: y[0] if isinstance(y, list) else y)

     # Normalize and threshold
    df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
        lambda x: x/x.max() if x.max() > 0 else 0)
    df.loc[df['intensity'] < threshold, 'intensity'] = 0

    # Apply processing
    df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
        lambda x: x.rolling(window_size, min_periods=min_periods, 
                           center=True).mean().fillna(0))
    
    # Normalize and threshold
    df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
        lambda x: x/x.max() if x.max() > 0 else 0)
    df.loc[df['intensity'] < threshold, 'intensity'] = 0

    # Create a mask for non-zero intensities
    nonzero_mask = df['intensity'] > 0

    # Group by kx, ky coordinates
    coords_group = df[nonzero_mask].groupby(['kx', 'ky'])

    # Find the binding energy to keep for each coordinate
    if suppress_at == 'max':
        energy_to_keep_sub = coords_group['binding_energy'].transform('max')
    elif suppress_at == 'min':
        energy_to_keep_sub = coords_group['binding_energy'].transform('min')
    elif suppress_at == 'none':
        # Return processed data without suppression
        processed_data = df.set_index(['binding_energy', 'kx', 'ky'])
        np.save('processed_data.npy', processed_data.reset_index().to_numpy())
        return processed_data
    else:
        energy_to_keep_sub = coords_group['binding_energy'].transform('min')

    # Reindex the result from the non-zero subset to the full DataFrame index
    energy_to_keep = pd.Series(np.nan, index=df.index)
    energy_to_keep.loc[nonzero_mask] = energy_to_keep_sub

    # Create a boolean mask for points to keep
    keep_mask = (df['binding_energy'] == energy_to_keep) & nonzero_mask

    # Set intensity to 0 for all points except those we want to keep
    df.loc[~keep_mask, 'intensity'] = 0

    # Return processed data
    processed_data = df.set_index(['binding_energy', 'kx', 'ky'])
    np.save('processed_data.npy', processed_data.reset_index().to_numpy())
    
    return processed_data

def plot_filtered_data(filename='processed_data.npy', downsample_fraction=0.1, min_energy=None, max_energy=None, point_size=1):
    # Load data from numpy file
    loaded_data = np.load(filename)
    plot_data = pd.DataFrame(loaded_data, columns=['binding_energy', 'kx', 'ky', 'intensity'])
    
    # Filter nonzero points
    plot_data = plot_data[plot_data['intensity'] > 0]
    
    # Filter by energy range if specified
    if min_energy is not None:
        plot_data = plot_data[plot_data['binding_energy'] >= min_energy]
    if max_energy is not None:
        plot_data = plot_data[plot_data['binding_energy'] <= max_energy]
    
    if downsample_fraction < 1 and len(plot_data) > 0:
        plot_data = plot_data.sample(frac=downsample_fraction)
    
    fig = px.scatter_3d(plot_data, 
                        x='kx', 
                        y='ky', 
                        z='binding_energy',
                        color='intensity',
                        color_continuous_scale='viridis',
                        range_color=[0, 1],  
                        labels={
                            'kx': 'kx',
                            'ky': 'ky',
                            'binding_energy': 'Binding Energy',
                            'intensity': 'Normalized Intensity'
                        })
    
    # Make points smaller
    fig.update_traces(marker=dict(size=point_size))
    fig.show()

# Call the functions
data_processed = process_threshold_and_filter(data_EK, 0.10, window_size=10, min_periods=1, suppress_at='none')

from plotly import graph_objects as go
def plot_filtered_data(filename='processed_data.npy', downsample_fraction=0.1, point_size=1,
                       kx_range=None, ky_range=None, energy_range=None,
                       show_top_plane=True, show_bottom_plane=True):
    # Load data from numpy file
    loaded_data = np.load(filename)
    plot_data = pd.DataFrame(loaded_data, columns=['binding_energy', 'kx', 'ky', 'intensity'])
    min_energy = energy_range[0] if energy_range is not None else None
    max_energy = energy_range[1] if energy_range is not None else None
    # Filter nonzero points
    plot_data = plot_data[plot_data['intensity'] > 0]
    
    # Filter by energy range if specified
    if min_energy is not None:
        plot_data = plot_data[plot_data['binding_energy'] >= min_energy]
    if max_energy is not None:
        plot_data = plot_data[plot_data['binding_energy'] <= max_energy]
    
    if downsample_fraction < 1 and len(plot_data) > 0:
        plot_data = plot_data.sample(frac=downsample_fraction)
    
    # Get min and max intensity for dynamic color range
    min_intensity = plot_data['intensity'].min()
    max_intensity = plot_data['intensity'].max()
    
    # Determine energy, kx, and ky ranges for planes
    actual_min_energy = min_energy if min_energy is not None else plot_data['binding_energy'].min()
    actual_max_energy = max_energy if max_energy is not None else plot_data['binding_energy'].max()
    actual_min_kx = kx_range[0] if kx_range is not None else plot_data['kx'].min()
    actual_max_kx = kx_range[1] if kx_range is not None else plot_data['kx'].max()
    actual_min_ky = ky_range[0] if ky_range is not None else plot_data['ky'].min()
    actual_max_ky = ky_range[1] if ky_range is not None else plot_data['ky'].max()
    
    # Create main scatter plot
    fig = px.scatter_3d(plot_data, 
                        x='kx', 
                        y='ky', 
                        z='binding_energy',
                        color='intensity',
                        color_continuous_scale='rainbow',
                        range_color=[min_intensity, max_intensity])
    
    # Create scene dictionary with axis labels
    scene_dict = dict(
        xaxis_title='k<sub>x</sub> [Å<sup>-1</sup>]',
        yaxis_title='k<sub>y</sub> [Å<sup>-1</sup>]',
        zaxis_title='E-E<sub>f</sub> [eV]',
        aspectmode='cube',  # Set aspect ratio to 1:1:1
    )
    


    # Add axis ranges if specified
    if kx_range is not None:
        scene_dict['xaxis'] = dict(range=kx_range)
    if ky_range is not None:
        scene_dict['yaxis'] = dict(range=ky_range)
    if energy_range is not None:
        scene_dict['zaxis'] = dict(range=energy_range)
    
    # Update layout with scene settings
    fig.update_layout(
        scene=scene_dict,
        coloraxis_colorbar=dict(
            title='Normalized Intensity'
        )
    )
    
    # Make points smaller
    fig.update_traces(marker=dict(size=point_size))
    

    # Create grid for planes - only if at least one plane will be shown
    if show_top_plane or show_bottom_plane:
        x_grid = np.linspace(actual_min_kx, actual_max_kx, 10)
        y_grid = np.linspace(actual_min_ky, actual_max_ky, 10)
        X, Y = np.meshgrid(x_grid, y_grid)
    
        # Add transparent yellow plane at top energy boundary
        if show_top_plane:
            Z_top = np.full_like(X, actual_max_energy)
            fig.add_trace(
                go.Surface(
                    x=X, y=Y, z=Z_top,
                    colorscale=[[0, 'yellow'], [1, 'yellow']],
                    showscale=False,
                    opacity=0.3,
                    name="Top Energy Plane"
                )
            )
        
        # Add transparent red plane at bottom energy boundary
        if show_bottom_plane:
            Z_bottom = np.full_like(X, actual_min_energy)
            fig.add_trace(
                go.Surface(
                    x=X, y=Y, z=Z_bottom,
                    colorscale=[[0, 'red'], [1, 'red']],
                    showscale=False,
                    opacity=0.3,
                    name="Bottom Energy Plane"
                )
            )
    
    # Show in browser tab instead of notebook
    fig.show(renderer="browser")

plot_filtered_data(downsample_fraction=0.05, point_size=2, kx_range=[-0.07, 0.07], ky_range=[-0.07, 0.07], energy_range=[-1, -0.15], show_top_plane=False)

import numpy as np
import pandas as pd
import plotly.graph_objects as go
from scipy.interpolate import griddata
from scipy.ndimage import gaussian_filter
import time

def find_critical_points(filename='processed_data.npy', energy_step=0.01, 
                         grid_size=100, intensity_threshold=0.01,
                         smoothing_sigma=1.0, gradient_threshold=1e-5,
                         kx_range=None, ky_range=None, energy_range=None,
                         kx_offset=0.0, ky_offset=0.0, energy_offset=0.0):
    """
    Find and classify critical points on the ARPES data surface.
    
    Parameters:
    -----------
    filename : str
        Path to the processed_data.npy file
    energy_step : float
        Step size for binding energy slices
    grid_size : int
        Size of the interpolation grid
    intensity_threshold : float
        Minimum intensity value to consider
    smoothing_sigma : float
        Sigma parameter for Gaussian smoothing
    gradient_threshold : float
        Threshold for considering a point as a critical point
    kx_range : tuple or None
        (min_kx, max_kx) range to include
    ky_range : tuple or None
        (min_ky, max_ky) range to include
    energy_range : tuple or None
        (min_energy, max_energy) range to include
    kx_offset : float
        Offset to apply to kx values in results
    ky_offset : float
        Offset to apply to ky values in results
    energy_offset : float
        Offset to apply to binding energy values in results
    
    Returns:
    --------
    dict
        Dictionary containing critical points and their classification
    """
    print("Loading and processing data...")
    start_time = time.time()
    
    # Load data
    data = np.load(filename)
    df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])
    
    # Apply filters
    df = df[df['intensity'] > intensity_threshold]
    
    if kx_range is not None:
        df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]
    
    if ky_range is not None:
        df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]
    
    if energy_range is not None:
        df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]
    
    # Define grid limits
    if kx_range is not None:
        kx_min, kx_max = kx_range
    else:
        kx_min, kx_max = df['kx'].min(), df['kx'].max()
    
    if ky_range is not None:
        ky_min, ky_max = ky_range
    else:
        ky_min, ky_max = df['ky'].min(), df['ky'].max()
    
    if energy_range is not None:
        min_energy, max_energy = energy_range
    else:
        min_energy = df['binding_energy'].min()
        max_energy = df['binding_energy'].max()
    
    # Create grid arrays
    energy_values = np.arange(min_energy, max_energy, energy_step)
    if max_energy - energy_values[-1] > 0.01 * energy_step:
        energy_values = np.append(energy_values, max_energy)
    
    kx_grid = np.linspace(kx_min, kx_max, grid_size)
    ky_grid = np.linspace(ky_min, ky_max, grid_size)
    KX, KY = np.meshgrid(kx_grid, ky_grid)
    
    # Build 3D intensity array
    intensity_grid = np.zeros((len(energy_values), grid_size, grid_size))
    
    print(f"Processing {len(energy_values)} energy slices...")
    
    # Interpolate intensity for each energy slice
    for i, energy in enumerate(energy_values):
        if i % 10 == 0:
            print(f"Processing slice {i}/{len(energy_values)}, energy={energy:.3f}")
        
        # Filter data for current energy bin
        if i == len(energy_values) - 1:
            slice_data = df[(df['binding_energy'] >= energy) & 
                          (df['binding_energy'] <= max_energy)]
        else:
            slice_data = df[(df['binding_energy'] >= energy) & 
                          (df['binding_energy'] < energy_values[i+1])]
        
        if len(slice_data) > 3:
            intensity_values = griddata(
                (slice_data['kx'], slice_data['ky']), 
                slice_data['intensity'],
                (KX, KY),
                method='linear',
                fill_value=0
            )
            
            intensity_grid[i] = intensity_values
    
    # Apply Gaussian smoothing to intensity grid
    print("Applying smoothing to intensity data...")
    smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)
    
    # Compute numerical gradients
    print("Computing gradients for critical point detection...")
    grad_e, grad_ky, grad_kx = np.gradient(smoothed_intensity)
    
    # Find potential critical points
    print("Finding critical points...")
    # Points where all gradient components are close to zero
    critical_mask = (np.abs(grad_e) < gradient_threshold) & \
                    (np.abs(grad_ky) < gradient_threshold) & \
                    (np.abs(grad_kx) < gradient_threshold)
    
    # Get coordinates of critical points
    e_indices, ky_indices, kx_indices = np.where(critical_mask)
    
    print(f"Found {len(e_indices)} potential critical points")
    
    # Analyze critical points with Hessian matrix
    print("Classifying critical points using Hessian matrices...")
    
    minima = []
    maxima = []
    saddle_points = []
    
    for i in range(len(e_indices)):
        # Skip points at the boundary of the grid
        if (e_indices[i] == 0 or e_indices[i] == len(energy_values)-1 or
            ky_indices[i] == 0 or ky_indices[i] == grid_size-1 or
            kx_indices[i] == 0 or kx_indices[i] == grid_size-1):
            continue
        
        # Compute Hessian matrix
        hessian = np.zeros((3, 3))
        
        # Second derivatives
        hessian[0, 0] = (smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]] - 
                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + 
                          smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]])
        
        hessian[1, 1] = (smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]] - 
                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + 
                          smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]])
        
        hessian[2, 2] = (smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]+1] - 
                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + 
                          smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]-1])
        
        # Mixed derivatives
        hessian[0, 1] = hessian[1, 0] = (
            (smoothed_intensity[e_indices[i]+1, ky_indices[i]+1, kx_indices[i]] - 
             smoothed_intensity[e_indices[i]+1, ky_indices[i]-1, kx_indices[i]] - 
             smoothed_intensity[e_indices[i]-1, ky_indices[i]+1, kx_indices[i]] + 
             smoothed_intensity[e_indices[i]-1, ky_indices[i]-1, kx_indices[i]]) / 4.0
        )
        
        hessian[0, 2] = hessian[2, 0] = (
            (smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]+1] - 
             smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]-1] - 
             smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]+1] + 
             smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]-1]) / 4.0
        )
        
        hessian[1, 2] = hessian[2, 1] = (
            (smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]+1] - 
             smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]-1] - 
             smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]+1] + 
             smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]-1]) / 4.0
        )
        
        # Calculate eigenvalues to determine the type of critical point
        eigenvalues = np.linalg.eigvalsh(hessian)
        
        # Get physical coordinates
        e_value = energy_values[e_indices[i]]
        ky_value = ky_grid[ky_indices[i]]
        kx_value = kx_grid[kx_indices[i]]
        intensity_value = smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]]
        
        # Apply offsets to the coordinates for reporting
        e_value_offset = e_value + energy_offset
        ky_value_offset = ky_value + ky_offset
        kx_value_offset = kx_value + kx_offset
        
        # Classify based on eigenvalues
        if np.all(eigenvalues > 0):
            minima.append((e_value_offset, ky_value_offset, kx_value_offset, intensity_value))
        elif np.all(eigenvalues < 0):
            maxima.append((e_value_offset, ky_value_offset, kx_value_offset, intensity_value))
        else:
            saddle_points.append((e_value_offset, ky_value_offset, kx_value_offset, intensity_value))
    
    # Calculate the result
    critical_sum = len(minima) + len(maxima) - len(saddle_points)
    
    print(f"\nCritical Point Analysis Results:")
    print(f"Minima: {len(minima)}")
    print(f"Maxima: {len(maxima)}")
    print(f"Saddle points: {len(saddle_points)}")
    print(f"Sum (minima + maxima - saddle points): {critical_sum}")
    
    results = {
        'minima': minima,
        'maxima': maxima,
        'saddle_points': saddle_points,
        'critical_sum': critical_sum
    }
    
    print(f"Total processing time: {time.time() - start_time:.2f}s")
    return results

def create_energy_surface(filename='processed_data.npy', energy_step=None, 
                          grid_size=100, intensity_threshold=0.01,
                          colorscale='Viridis', smoothing_sigma=1.0,
                          surface_intensity_threshold=0.1, single_color_mode=False,
                          surface_color='rgb(70,130,180)',
                          kx_range=None, ky_range=None, energy_range=None,
                          kx_offset=0.0, ky_offset=0.0, energy_offset=0.0,
                          show_yellow_plane=False, yellow_plane_energy=None,
                          show_red_plane=False, red_plane_energy=None):
    """
    Create a 3D surface visualization from processed ARPES data.
    
    Parameters:
    -----------
    filename : str
        Path to the processed_data.npy file
    energy_step : float or None
        Step size for binding energy slices. If None, use unique values
    grid_size : int
        Size of the interpolation grid (grid_size × grid_size)
    intensity_threshold : float
        Minimum intensity value to consider (for filtering noise)
    colorscale : str
        Plotly colorscale to use for the surface
    smoothing_sigma : float
        Sigma parameter for Gaussian smoothing (higher = more smoothing)
    surface_intensity_threshold : float
        Threshold for including points in the surface visualization
    single_color_mode : bool
        If True, use a single color for the surface instead of intensity coloring
    surface_color : str
        RGB or named color to use if single_color_mode is True
    kx_range : tuple or None
        (min_kx, max_kx) range to include. If None, use full range.
    ky_range : tuple or None
        (min_ky, max_ky) range to include. If None, use full range.
    energy_range : tuple or None
        (min_energy, max_energy) range to include. If None, use full range.
    kx_offset : float
        Offset to apply to kx values for display
    ky_offset : float
        Offset to apply to ky values for display
    energy_offset : float
        Offset to apply to binding energy values for display
    show_yellow_plane : bool
        If True, show a transparent yellow plane at yellow_plane_energy
    yellow_plane_energy : float or None
        Binding energy value for the yellow plane
    show_red_plane : bool
        If True, show a transparent red plane at red_plane_energy
    red_plane_energy : float or None
        Binding energy value for the red plane
    
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        The 3D surface visualization
    """
    print("Loading data...")
    start_time = time.time()
    # Load data
    data = np.load(filename)
    df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])
    
    # Filter by intensity to remove noise and reduce computational load
    df = df[df['intensity'] > intensity_threshold]
    
    # Apply range filters if specified
    if kx_range is not None:
        print(f"Filtering kx range: {kx_range}")
        df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]
    
    if ky_range is not None:
        print(f"Filtering ky range: {ky_range}")
        df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]
    
    if energy_range is not None:
        print(f"Filtering energy range: {energy_range}")
        df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]
    
    print(f"Data loaded with {len(df)} points. Time: {time.time() - start_time:.2f}s")
    
    # Check if we have enough data after filtering
    if len(df) < 10:
        print("Warning: Very few data points remain after filtering. Visualization may be poor.")
    
    # Define energy values strictly within the specified range
    if energy_step is None:
        # For None step size, use exact unique values
        energy_values = sorted(df['binding_energy'].unique())
    else:
        # For specified step size, create evenly spaced values
        if energy_range is not None:
            min_energy, max_energy = energy_range
        else:
            min_energy = df['binding_energy'].min()
            max_energy = df['binding_energy'].max()
            
        # Create energy values strictly within range
        # Use exclusive max value to ensure we don't exceed the range
        energy_values = np.arange(min_energy, max_energy, energy_step)
        
        # Handle edge case: ensure max_energy is included if very close to range boundary
        if max_energy - energy_values[-1] > 0.01 * energy_step:
            energy_values = np.append(energy_values, max_energy)
    
    # Create a common grid for all slices
    if kx_range is not None:
        kx_min, kx_max = kx_range
    else:
        kx_min, kx_max = df['kx'].min(), df['kx'].max()
    
    if ky_range is not None:
        ky_min, ky_max = ky_range
    else:
        ky_min, ky_max = df['ky'].min(), df['ky'].max()
    
    kx_grid = np.linspace(kx_min, kx_max, grid_size)
    ky_grid = np.linspace(ky_min, ky_max, grid_size)
    KX, KY = np.meshgrid(kx_grid, ky_grid)
    
    # Initialize 3D arrays for surface
    Z = np.zeros((len(energy_values), grid_size, grid_size))
    intensity_grid = np.zeros((len(energy_values), grid_size, grid_size))
    
    print(f"Processing {len(energy_values)} energy slices in range [{energy_values[0]:.3f}, {energy_values[-1]:.3f}]")
    
    # Process each energy slice
    for i, energy in enumerate(energy_values):
        if i % 10 == 0:
            print(f"Processing slice {i}/{len(energy_values)}, energy={energy:.3f}")
        
        # Filter data for current energy (or energy bin)
        if energy_step is None:
            slice_data = df[df['binding_energy'] == energy]
        else:
            # For last slice, include everything up to max_energy
            if i == len(energy_values) - 1:
                slice_data = df[(df['binding_energy'] >= energy) & 
                              (df['binding_energy'] <= max_energy)]
            else:
                slice_data = df[(df['binding_energy'] >= energy) & 
                              (df['binding_energy'] < energy_values[i+1])]
        
        if len(slice_data) > 3:  # Need at least 3 points for interpolation
            # Interpolate intensity values to the grid
            intensity_values = griddata(
                (slice_data['kx'], slice_data['ky']), 
                slice_data['intensity'],
                (KX, KY),
                method='linear',
                fill_value=0
            )
            
            # Store the interpolated values
            Z[i] = np.full((grid_size, grid_size), energy)
            intensity_grid[i] = intensity_values
    
    # Apply Gaussian smoothing to intensity grid
    print("Applying smoothing to the data...")
    smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)
    
    print(f"Processing complete. Time: {time.time() - start_time:.2f}s")
    
    # Create 3D meshgrids
    X_3d = np.repeat(KX[np.newaxis, :, :], len(energy_values), axis=0)
    Y_3d = np.repeat(KY[np.newaxis, :, :], len(energy_values), axis=0)
    
    # Apply offsets to the coordinate grids for visualization
    X_3d_display = X_3d + kx_offset
    Y_3d_display = Y_3d + ky_offset
    Z_display = Z + energy_offset
    
    # Create figure
    fig = go.Figure()
    
    # Apply the surface intensity threshold
    print(f"Applying surface intensity threshold: {surface_intensity_threshold}")
    intensity_values = smoothed_intensity.flatten()
    
    # Only include points that are above the threshold
    mask = intensity_values >= surface_intensity_threshold
    
    print(f"Filtered surface contains {np.sum(mask)} points out of {len(mask)}")
    
    # Create either a colored surface or single-color surface based on the toggle
    if single_color_mode:
        print("Creating single-color surface visualization...")
        # For single color mode, we create a binary mask for the isosurface
        binary_values = np.zeros_like(smoothed_intensity.flatten())
        binary_values[mask] = 1.0
        
        fig.add_trace(go.Isosurface(
            x=X_3d_display.flatten(),  # Use offset-adjusted coordinates
            y=Y_3d_display.flatten(),  # Use offset-adjusted coordinates
            z=Z_display.flatten(),     # Use offset-adjusted coordinates
            value=binary_values,
            isomin=0.5,  # Binary threshold
            isomax=1.0,
            surface_count=1,  # Only one surface for binary data
            opacity=0.9,
            colorscale=[[0, 'rgba(0,0,0,0)'], [1, surface_color]],  # Single color for surface
            caps=dict(x_show=False, y_show=False, z_show=False),
            showscale=False  # Hide colorbar
        ))
    else:
        print("Creating intensity-colored surface visualization...")
        # If we want a colored surface based on intensity
        fig.add_trace(go.Isosurface(
            x=X_3d_display.flatten(),  # Use offset-adjusted coordinates
            y=Y_3d_display.flatten(),  # Use offset-adjusted coordinates
            z=Z_display.flatten(),     # Use offset-adjusted coordinates
            value=intensity_values,
            isomin=surface_intensity_threshold,
            isomax=np.max(intensity_values) * 0.9, # Top 10% for high intensity regions
            surface_count=20,
            opacity=0.99999999,
            colorscale=colorscale,
            caps=dict(x_show=False, y_show=False, z_show=False)
        ))
    
    # Add yellow energy plane if enabled
    if show_yellow_plane and yellow_plane_energy is not None:
        # Verify energy is within range
        if (energy_range is None or 
            (energy_range[0] <= yellow_plane_energy <= energy_range[1])):
            print(f"Adding yellow plane at energy = {yellow_plane_energy}")
            # Create a mesh for the plane with offset applied
            plane_z = np.full((2, 2), yellow_plane_energy + energy_offset)  # Apply energy offset
            plane_x = np.array([[kx_min + kx_offset, kx_max + kx_offset],  # Apply kx offset
                                [kx_min + kx_offset, kx_max + kx_offset]])
            plane_y = np.array([[ky_min + ky_offset, ky_min + ky_offset],  # Apply ky offset
                                [ky_max + ky_offset, ky_max + ky_offset]])
            
            fig.add_trace(go.Surface(
                z=plane_z,
                x=plane_x,
                y=plane_y,
                colorscale=[[0, 'rgba(255,255,0,0.3)'], [1, 'rgba(255,255,0,0.3)']],
                showscale=False,
                name=f"E = {yellow_plane_energy + energy_offset:.3f}",  # Update label with offset
                hoverinfo="name",
                opacity=0.5
            ))
    
    # Add red energy plane if enabled
    if show_red_plane and red_plane_energy is not None:
        # Verify energy is within range
        if (energy_range is None or 
            (energy_range[0] <= red_plane_energy <= energy_range[1])):
            print(f"Adding red plane at energy = {red_plane_energy}")
            # Create a mesh for the plane with offset applied
            plane_z = np.full((2, 2), red_plane_energy + energy_offset)  # Apply energy offset
            plane_x = np.array([[kx_min + kx_offset, kx_max + kx_offset],  # Apply kx offset
                                [kx_min + kx_offset, kx_max + kx_offset]])
            plane_y = np.array([[ky_min + ky_offset, ky_min + ky_offset],  # Apply ky offset
                                [ky_max + ky_offset, ky_max + ky_offset]])
            
            fig.add_trace(go.Surface(
                z=plane_z,
                x=plane_x,
                y=plane_y,
                colorscale=[[0, 'rgba(255,0,0,0.3)'], [1, 'rgba(255,0,0,0.3)']],
                showscale=False,
                name=f"E = {red_plane_energy + energy_offset:.3f}",  # Update label with offset
                hoverinfo="name",
                opacity=0.5
            ))
    
    # Determine axis range labels for the title
    kx_label = f"kx: [{kx_min:.2f} to {kx_max:.2f}]"
    ky_label = f"ky: [{ky_min:.2f} to {ky_max:.2f}]"
    energy_label = f"E: [{energy_values[0]:.2f} to {energy_values[-1]:.2f}]"
    range_title = f"{kx_label}, {ky_label}, {energy_label}"
    
    # Update layout with range information
    fig.update_layout(
        title=f"ARPES Data 3D Surface<br><sub>{range_title}</sub>",
        scene=dict(
            xaxis_title=r"k<sub>x</sub> (1/Å)",
            yaxis_title=r"k<sub>y</sub> (1/Å)",
            zaxis_title=r"E<sub>b</sub> (eV)",
            aspectratio=dict(x=1, y=1, z=1),  # Equal scale for all axes
            # Explicitly set axis ranges to match our constraints with offsets
            xaxis_range=[kx_min + kx_offset, kx_max + kx_offset],
            yaxis_range=[ky_min + ky_offset, ky_max + ky_offset],
            zaxis_range=[energy_values[0] + energy_offset, energy_values[-1] + energy_offset],
            xaxis=dict(tickfont=dict(size=12), title_font=dict(size=20)),
            yaxis=dict(tickfont=dict(size=12), title_font=dict(size=20)),
            zaxis=dict(tickfont=dict(size=12), title_font=dict(size=20))
        ),
        margin=dict(l=0, r=0, b=0, t=60)  # Increased top margin for the subtitle
    )
    
    print(f"Total processing time: {time.time() - start_time:.2f}s")
    return fig

# Example usage with energy range constraint:
fig = create_energy_surface(
    energy_step=0.01,
    grid_size=200,
    smoothing_sigma=1.3,
    surface_intensity_threshold=0.2,
    energy_range=(-1.1, 0.1),
    ky_range=(-0.9, 0.1),
    kx_range=(-0.5, 0.5),
    kx_offset=0.4,  # Example offset for kx
    ky_offset=0.0,  # Example offset for ky
    energy_offset=0.0,  # Example offset for binding energy
    show_yellow_plane=True,
    yellow_plane_energy=0,
    show_red_plane=True,
    red_plane_energy=-1.0,
    single_color_mode=True
)
fig.show(renderer="browser")



import numpy as np
import pandas as pd
import plotly.graph_objects as go
from scipy.interpolate import griddata
from scipy.ndimage import gaussian_filter
import time
def create_integrated_projections_from_surface(filename='processed_data.npy', 
                                 grid_size=200, 
                                 intensity_threshold=0.01,
                                 smoothing_sigma=1.0,
                                 binary_threshold=0.15,
                                 single_color_mode=False,
                                 kx_range=None, 
                                 ky_range=None, 
                                 energy_range=None,
                                 kx_offset=0.0,
                                 ky_offset=0.0,
                                 energy_offset=0.0,
                                 colorscale='Viridis',
                                 show_all=True):
    """
    Create integrated projections from processed 3D ARPES surface data.
    
    Parameters:
    -----------
    filename : str
        Path to the processed_data.npy file
    grid_size : int
        Size of the interpolation grid
    intensity_threshold : float
        Minimum intensity value to consider (for filtering noise)
    smoothing_sigma : float
        Sigma parameter for Gaussian smoothing
    binary_threshold : float
        Threshold for binarizing intensity in single_color_mode
    single_color_mode : bool
        If True, binarize intensities before integration
    kx_range : tuple or None
        (min_kx, max_kx) range to include
    ky_range : tuple or None
        (min_ky, max_ky) range to include
    energy_range : tuple or None
        (min_energy, max_energy) range to include
    kx_offset : float
        Offset to apply to kx values for display
    ky_offset : float
        Offset to apply to ky values for display
    energy_offset : float
        Offset to apply to binding energy values for display
    colorscale : str
        Plotly colorscale to use
    show_all : bool
        If True, display all three projections; if False, return the figures
        
    Returns:
    --------
    dict or None
        Dictionary containing the three projection figures if show_all=False
    """
    print("Loading and processing data...")
    start_time = time.time()
    
    # Load data
    data = np.load(filename)
    df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])
    
    # Filter by intensity to remove noise
    df = df[df['intensity'] > intensity_threshold]
    
    # Apply range filters if specified
    if kx_range is not None:
        df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]
    
    if ky_range is not None:
        df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]
    
    if energy_range is not None:
        df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]
    
    # Define grid limits
    kx_min = kx_range[0] if kx_range is not None else df['kx'].min()
    kx_max = kx_range[1] if kx_range is not None else df['kx'].max()
    
    ky_min = ky_range[0] if ky_range is not None else df['ky'].min()
    ky_max = ky_range[1] if ky_range is not None else df['ky'].max()
    
    energy_min = energy_range[0] if energy_range is not None else df['binding_energy'].min()
    energy_max = energy_range[1] if energy_range is not None else df['binding_energy'].max()
    
    # Create grid arrays
    kx_grid = np.linspace(kx_min, kx_max, grid_size)
    ky_grid = np.linspace(ky_min, ky_max, grid_size)
    energy_grid = np.linspace(energy_max, energy_min, grid_size)
    
    # Create 3D meshgrids
    KX, KY, E = np.meshgrid(kx_grid, ky_grid, energy_grid, indexing='ij')
    
    # Build 3D intensity array - this is the key step for continuous surface
    print("Building 3D intensity grid...")
    intensity_grid = np.zeros((grid_size, grid_size, grid_size))
    
    # For each energy slice, interpolate intensity
    for k, energy in enumerate(energy_grid):
        if k % 20 == 0:
            print(f"Processing energy slice {k}/{grid_size}, energy={energy:.3f}")
        
        # Filter data for current energy bin
        energy_step = (energy_max - energy_min) / grid_size
        slice_data = df[(df['binding_energy'] >= energy - energy_step/2) & 
                      (df['binding_energy'] < energy + energy_step/2)]
        
        if len(slice_data) > 3:  # Need at least 3 points for interpolation
            # Interpolate intensity values to the grid
            points = np.column_stack((slice_data['kx'], slice_data['ky']))
            values = slice_data['intensity']
            
            # Create a 2D grid for this energy slice
            grid_x, grid_y = np.meshgrid(kx_grid, ky_grid, indexing='ij')
            grid_points = np.column_stack((grid_x.flatten(), grid_y.flatten()))
            
            # Interpolate
            grid_values = griddata(points, values, grid_points, method='linear', fill_value=0)
            intensity_grid[:, :, k] = grid_values.reshape(grid_size, grid_size)
    
    # Apply Gaussian smoothing to intensity grid for continuity
    print("Applying 3D smoothing...")
    smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)
    
    # Apply binary threshold if in single color mode
    if single_color_mode:
        print(f"Applying binary threshold {binary_threshold}...")
        binary_intensity = (smoothed_intensity >= binary_threshold).astype(float)
        processed_intensity = binary_intensity
    else:
        processed_intensity = smoothed_intensity
    
    # Create projections by integrating along each axis
    print("Creating projections by integration...")
    
    # 1. E vs kx projection (integrating over ky)
    e_kx_projection = np.sum(processed_intensity, axis=1).T  # Sum along ky axis
    
    # 2. E vs ky projection (integrating over kx)
    e_ky_projection = np.sum(processed_intensity, axis=0).T  # Sum along kx axis
    
    # 3. kx vs ky projection (integrating over energy)
    kx_ky_projection = np.sum(processed_intensity, axis=2)   # Sum along energy axis
    
    # Apply offsets to the grid values for display
    kx_grid_display = kx_grid + kx_offset
    ky_grid_display = ky_grid + ky_offset
    energy_grid_display = energy_grid + energy_offset
    
    # Create figures
    print("Creating visualization plots...")
    # 1. E vs kx projection
    fig_e_kx = go.Figure(data=go.Heatmap(
        z=e_kx_projection,
        x=kx_grid_display,  # Apply kx offset
        y=energy_grid_display,  # Apply energy offset
        colorscale=colorscale,
        zmin=0,
        zmax=None,  # Auto-scale maximum
    ))
    
    fig_e_kx.update_layout(
        title=r"E<sub>b</sub> vs k<sub>x</sub> Projection (Integrated over k<sub>y</sub>)",
        xaxis_title=r"k<sub>x</sub> (1/Å)",
        yaxis_title=r"E<sub>b</sub> (eV)",
        width=700,
        height=600,
        xaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16)),
        yaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16))
    )
    
    # 2. E vs ky projection
    fig_e_ky = go.Figure(data=go.Heatmap(
        z=e_ky_projection,
        x=ky_grid_display,  # Apply ky offset
        y=energy_grid_display,  # Apply energy offset
        colorscale=colorscale,
        zmin=0,
        zmax=None,  # Auto-scale maximum
    ))
    
    fig_e_ky.update_layout(
        title=r"E<sub>b</sub> vs k<sub>y</sub> Projection (Integrated over k<sub>x</sub>)",
        xaxis_title=r"k<sub>y</sub> (1/Å)",
        yaxis_title=r"E<sub>b</sub> (eV)",
        width=700,
        height=600,
        xaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16)),
        yaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16))
    )
    
    # 3. kx vs ky projection
    fig_kx_ky = go.Figure(data=go.Heatmap(
        z=kx_ky_projection,
        x=kx_grid_display,  # Apply kx offset
        y=ky_grid_display,  # Apply ky offset
        colorscale=colorscale,
        zmin=0,
        zmax=None,  # Auto-scale maximum
    ))
    
    fig_kx_ky.update_layout(
        title=r"k<sub>x</sub> vs k<sub>y</sub> Projection (Integrated over Binding Energy)",
        xaxis_title=r"k<sub>x</sub> (1/Å)",
        yaxis_title=r"k<sub>y</sub> (1/Å)",
        width=700,
        height=600,
        xaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16)),
        yaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16))
    )
    
    print(f"Total processing time: {time.time() - start_time:.2f}s")
    
    if show_all:
        fig_e_kx.show()
        fig_e_ky.show()
        fig_kx_ky.show()
        return None
    else:
        return {
            'e_kx': fig_e_kx,
            'e_ky': fig_e_ky,
            'kx_ky': fig_kx_ky
        }

# Example usage:
projections = create_integrated_projections_from_surface(
    grid_size=100,
    smoothing_sigma=1,
    binary_threshold=0.15,
    single_color_mode=True,  # Set to True for binary mode
    energy_range=(-1.1, -0.01),
    ky_range=(-0.9, 0.1),
    kx_range=(-0.5, 0.5),
    kx_offset=0.0,  # Example offset for kx
    ky_offset=0.4,  # Example offset for ky
    energy_offset=0.0,  # Example offset for binding energy
    colorscale='bluered',
    show_all=True
)


def create_energy_surface_with_critical_points(
    filename='processed_data.npy', 
    energy_step=0.01,
    grid_size=100, 
    intensity_threshold=0.01,
    colorscale='Viridis', 
    smoothing_sigma=1.0,
    surface_intensity_threshold=0.1, 
    single_color_mode=False,
    surface_color='rgb(70,130,180)',
    gradient_threshold=1e-4,
    kx_range=None, 
    ky_range=None, 
    energy_range=None):
    """
    Create a 3D ARPES surface visualization with critical points highlighted.
    
    Parameters:
    -----------
    [parameters as in original function, plus gradient_threshold]
    gradient_threshold : float
        Threshold to identify critical points
    
    Returns:
    --------
    fig : plotly.graph_objects.Figure
        The 3D surface visualization with critical points
    """
    print("Loading data...")
    start_time = time.time()
    
    # Load data
    data = np.load(filename)
    df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])
    
    # Filter by intensity to remove noise
    df = df[df['intensity'] > intensity_threshold]
    
    # Apply range filters if specified
    if kx_range is not None:
        print(f"Filtering kx range: {kx_range}")
        df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]
    
    if ky_range is not None:
        print(f"Filtering ky range: {ky_range}")
        df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]
    
    if energy_range is not None:
        print(f"Filtering energy range: {energy_range}")
        df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]
    
    print(f"Data loaded with {len(df)} points. Time: {time.time() - start_time:.2f}s")
    
    # Define energy values
    if energy_step is None:
        energy_values = sorted(df['binding_energy'].unique())
    else:
        if energy_range is not None:
            min_energy, max_energy = energy_range
        else:
            min_energy = df['binding_energy'].min()
            max_energy = df['binding_energy'].max()
            
        energy_values = np.arange(min_energy, max_energy, energy_step)
        if max_energy - energy_values[-1] > 0.01 * energy_step:
            energy_values = np.append(energy_values, max_energy)
    
    # Create grid for interpolation
    if kx_range is not None:
        kx_min, kx_max = kx_range
    else:
        kx_min, kx_max = df['kx'].min(), df['kx'].max()
    
    if ky_range is not None:
        ky_min, ky_max = ky_range
    else:
        ky_min, ky_max = df['ky'].min(), df['ky'].max()
    
    kx_grid = np.linspace(kx_min, kx_max, grid_size)
    ky_grid = np.linspace(ky_min, ky_max, grid_size)
    KX, KY = np.meshgrid(kx_grid, ky_grid)
    
    # Initialize 3D arrays for surface
    Z = np.zeros((len(energy_values), grid_size, grid_size))
    intensity_grid = np.zeros((len(energy_values), grid_size, grid_size))
    
    print(f"Processing {len(energy_values)} energy slices...")
    
    # Process each energy slice
    for i, energy in enumerate(energy_values):
        if i % 10 == 0:
            print(f"Processing slice {i}/{len(energy_values)}, energy={energy:.3f}")
        
        # Filter data for current energy
        if energy_step is None:
            slice_data = df[df['binding_energy'] == energy]
        else:
            if i == len(energy_values) - 1:
                slice_data = df[(df['binding_energy'] >= energy) & 
                              (df['binding_energy'] <= max_energy)]
            else:
                slice_data = df[(df['binding_energy'] >= energy) & 
                              (df['binding_energy'] < energy_values[i+1])]
        
        if len(slice_data) > 3:
            # Interpolate intensity values
            intensity_values = griddata(
                (slice_data['kx'], slice_data['ky']), 
                slice_data['intensity'],
                (KX, KY),
                method='linear',
                fill_value=0
            )
            
            # Store interpolated values
            Z[i] = np.full((grid_size, grid_size), energy)
            intensity_grid[i] = intensity_values
    
    # Apply Gaussian smoothing to intensity grid
    print("Applying smoothing to the data...")
    smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)
    
    # Find critical points
    print("Computing gradients for critical point detection...")
    grad_e, grad_ky, grad_kx = np.gradient(smoothed_intensity)
    
    # Points where all gradient components are close to zero
    critical_mask = (np.abs(grad_e) < gradient_threshold) & \
                    (np.abs(grad_ky) < gradient_threshold) & \
                    (np.abs(grad_kx) < gradient_threshold)
    
    # Get coordinates of critical points
    e_indices, ky_indices, kx_indices = np.where(critical_mask)
    
    print(f"Found {len(e_indices)} potential critical points")
    
    # Lists to store different types of critical points
    minima = []
    maxima = []
    saddle_points = []
    
    # Analyze critical points with Hessian matrix
    print("Classifying critical points using Hessian matrices...")
    for i in range(len(e_indices)):
        # Skip points at the boundary
        if (e_indices[i] == 0 or e_indices[i] == len(energy_values)-1 or
            ky_indices[i] == 0 or ky_indices[i] == grid_size-1 or
            kx_indices[i] == 0 or kx_indices[i] == grid_size-1):
            continue
        
        # Compute Hessian matrix
        hessian = np.zeros((3, 3))
        
        # Second derivatives
        hessian[0, 0] = (smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]] - 
                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + 
                          smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]])
        
        hessian[1, 1] = (smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]] - 
                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + 
                          smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]])
        
        hessian[2, 2] = (smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]+1] - 
                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + 
                          smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]-1])
        
        # Mixed derivatives
        hessian[0, 1] = hessian[1, 0] = (
            (smoothed_intensity[e_indices[i]+1, ky_indices[i]+1, kx_indices[i]] - 
             smoothed_intensity[e_indices[i]+1, ky_indices[i]-1, kx_indices[i]] - 
             smoothed_intensity[e_indices[i]-1, ky_indices[i]+1, kx_indices[i]] + 
             smoothed_intensity[e_indices[i]-1, ky_indices[i]-1, kx_indices[i]]) / 4.0
        )
        
        hessian[0, 2] = hessian[2, 0] = (
            (smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]+1] - 
             smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]-1] - 
             smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]+1] + 
             smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]-1]) / 4.0
        )
        
        hessian[1, 2] = hessian[2, 1] = (
            (smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]+1] - 
             smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]-1] - 
             smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]+1] + 
             smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]-1]) / 4.0
        )
        
        # Calculate eigenvalues 
        eigenvalues = np.linalg.eigvalsh(hessian)
        
        # Get physical coordinates
        e_value = energy_values[e_indices[i]]
        ky_value = ky_grid[ky_indices[i]]
        kx_value = kx_grid[kx_indices[i]]
        
        # Classify based on eigenvalues
        if np.all(eigenvalues > 0):
            minima.append((kx_value, ky_value, e_value))
        elif np.all(eigenvalues < 0):
            maxima.append((kx_value, ky_value, e_value))
        else:
            saddle_points.append((kx_value, ky_value, e_value))
    
    # Create 3D figure
    print("Creating surface visualization...")
    
    # Create 3D meshgrids
    X_3d = np.repeat(KX[np.newaxis, :, :], len(energy_values), axis=0)
    Y_3d = np.repeat(KY[np.newaxis, :, :], len(energy_values), axis=0)
    
    # Create figure
    fig = go.Figure()
    
    # Process intensity values for the surface
    intensity_values = smoothed_intensity.flatten()
    mask = intensity_values >= surface_intensity_threshold
    
    # Create the surface
    if single_color_mode:
        binary_values = np.zeros_like(intensity_values)
        binary_values[mask] = 1.0
        
        fig.add_trace(go.Isosurface(
            x=X_3d.flatten(),
            y=Y_3d.flatten(),
            z=Z.flatten(),
            value=binary_values,
            isomin=0.5,
            isomax=1.0,
            surface_count=1,
            opacity=0.8,  # Slightly transparent to see critical points
            colorscale=[[0, 'rgba(0,0,0,0)'], [1, surface_color]],
            caps=dict(x_show=False, y_show=False, z_show=False),
            showscale=False
        ))
    else:
        fig.add_trace(go.Isosurface(
            x=X_3d.flatten(),
            y=Y_3d.flatten(),
            z=Z.flatten(),
            value=intensity_values,
            isomin=surface_intensity_threshold,
            isomax=np.max(intensity_values) * 0.9,
            surface_count=20,
            opacity=0.8,  # Slightly transparent to see critical points
            colorscale=colorscale,
            caps=dict(x_show=False, y_show=False, z_show=False)
        ))
    
    # Add critical points to the plot
    # Saddle points - Blue
    if saddle_points:
        saddle_x, saddle_y, saddle_z = zip(*saddle_points)
        fig.add_trace(go.Scatter3d(
            x=saddle_x, y=saddle_y, z=saddle_z,
            mode='markers',
            marker=dict(size=6, color='blue'),
            name='Saddle Points'
        ))
    
    # Maxima - Yellow
    if maxima:
        max_x, max_y, max_z = zip(*maxima)
        fig.add_trace(go.Scatter3d(
            x=max_x, y=max_y, z=max_z,
            mode='markers',
            marker=dict(size=6, color='yellow'),
            name='Maxima'
        ))
    
    # Minima - Green
    if minima:
        min_x, min_y, min_z = zip(*minima)
        fig.add_trace(go.Scatter3d(
            x=min_x, y=min_y, z=min_z,
            mode='markers',
            marker=dict(size=6, color='green'),
            name='Minima'
        ))
    
    # Critical point summary
    critical_sum = len(minima) + len(maxima) - len(saddle_points)
    
    print(f"\nCritical Point Analysis Results:")
    print(f"Minima: {len(minima)}")
    print(f"Maxima: {len(maxima)}")
    print(f"Saddle points: {len(saddle_points)}")
    print(f"Sum (minima + maxima - saddle points): {critical_sum}")
    
    # Determine axis range labels for the title
    kx_label = f"kx: [{kx_min:.2f} to {kx_max:.2f}]"
    ky_label = f"ky: [{ky_min:.2f} to {ky_max:.2f}]"
    energy_label = f"E: [{energy_values[0]:.2f} to {energy_values[-1]:.2f}]"
    range_title = f"{kx_label}, {ky_label}, {energy_label}"
    
    # Create summary of critical points for the title
    critical_summary = f"Minima: {len(minima)}  |  Maxima: {len(maxima)}  |  Saddles: {len(saddle_points)}  |  Sum: {critical_sum}"
    
    # Update layout with range information and critical point summary
    fig.update_layout(
        title=f"ARPES Data with Critical Points<br><sub>{range_title}<br>{critical_summary}</sub>",
        scene=dict(
            xaxis_title="kx",
            yaxis_title="ky",
            zaxis_title="Binding Energy",
            aspectratio=dict(x=1, y=1, z=0.8),
            # Explicitly set axis ranges
            xaxis_range=[kx_min, kx_max],
            yaxis_range=[ky_min, ky_max],
            zaxis_range=[energy_values[0], energy_values[-1]]
        ),
        margin=dict(l=0, r=0, b=0, t=80),  # Increased top margin for subtitle
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    print(f"Total processing time: {time.time() - start_time:.2f}s")
    return fig, critical_sum


# Example usage with adjustable parameters
fig, critical_sum = create_energy_surface_with_critical_points(
    filename='processed_data.npy',
    energy_step=0.01,
    grid_size=100,
    smoothing_sigma=0.6,
    surface_intensity_threshold=0.15,
    energy_range=(-1.2, -0.3),  # Threshold for the surface rendering
    single_color_mode=False,   # False for colored intensity surface, True for solid color
    gradient_threshold=0.000000000001,   # Threshold for critical point detection
    kx_range=(-0.1, 0.1),      # kx range to analyze
    ky_range=(-0.1, 0.1),      # ky range to analyze  # Energy range to analyze
)

# Display the figure
fig.show(renderer="browser")

print(f"The final sum of critical points (minima + maxima - saddle points): {critical_sum}")


# Create surface from the exterior points
surface_data = create_surface_from_exterior_points(filename='processed_data.npy', ball_radius=0.0175)


# Optional: You can specify ranges
 plot_surface_data('surface_data.npy', kx_range=[-2, 2], ky_range=[-2, 2], energy_range=[-5, 0])




data_processed.values.shape

plot_surface_data(surface_data, kx_range=[-0.2, 0.2], ky_range=[-0.2, 0.2], energy_range=[-1, -0.3])

plot_filtered_data(filename='surface_data.npy',downsample_fraction=0.10, point_size=2, kx_range=[-0.1, 0.1], ky_range=[-0.1, 0.1], energy_range=[-1, -0.3])

import numpy as np

# Load the full data array
data = np.load('processed_data.npy')

# Filter rows to keep only those with non-zero intensity (4th column not 0)
filtered_data = data[data[:, 3] != 0].copy()

# For consistency, set all non-zero intensities to 1
filtered_data[:, 3] = 1

# Extract the first three columns to form the 3D point cloud
data_3d = filtered_data[:, :3]

data_3d

import numpy as np
from scipy.interpolate import griddata

# Load the data and remove rows with zero intensity.
data = np.load('processed_data.npy')
filtered_data = data[data[:, 3] != 0].copy()
filtered_data[:, 3] = 1  # Set non-zero intensities to exactly 1

# Drop the intensity column to get a (N,3) array.
data_3d = filtered_data[:, :3]

# Extract the x, y, z columns.
x_vals = data_3d[:, 0]
y_vals = data_3d[:, 1]
z_vals = data_3d[:, 2]

# Determine grid resolution.
# If the original data is roughly on a grid, you might compute the number of unique x's or y's.
# Otherwise, choose a resolution, e.g. n = 100.
n_unique_x = len(np.unique(x_vals))
n_unique_y = len(np.unique(y_vals))
if n_unique_x * n_unique_y == len(x_vals):
    # Data are already on a grid.
    n = n_unique_x  # assume square grid
else:
    n = 100  # set grid resolution as needed

# Define regular grid extents based on the data.
xi = np.linspace(np.min(x_vals), np.max(x_vals), n)
yi = np.linspace(np.min(y_vals), np.max(y_vals), n)
X_grid, Y_grid = np.meshgrid(xi, yi)

# Use scipy.interpolate.griddata to interpolate scattered z-values onto the regular (X,Y) grid.
Z_grid = griddata(points=(x_vals, y_vals), values=z_vals, xi=(X_grid, Y_grid), method='linear')

# Handle any NaNs that can arise if some grid points lie outside the convex hull.
# Here we fill NaN values using nearest-neighbor interpolation.
if np.isnan(Z_grid).any():
    # Find indices where interpolation yielded NaN.
    nan_mask = np.isnan(Z_grid)
    Z_grid[nan_mask] = griddata(points=(x_vals, y_vals), values=z_vals, 
                                xi=(X_grid[nan_mask], Y_grid[nan_mask]), method='nearest')

# Format grid data as a flat array (n^2, 3) for further analysis.
data_grid = np.column_stack((X_grid.flatten(), Y_grid.flatten(), Z_grid.flatten()))

# data_grid now holds the interpolated regular grid with columns [x, y, z].
print("Formatted grid data shape:", data_grid.shape)


data_grid.shape

data_grid2 = data_grid[:, [1, 2, 0]]
data_grid2

import numpy as np
import plotly.graph_objs as go
import plotly.offline as pyo
from sklearn.cluster import DBSCAN

def adaptive_cluster_candidates(candidates, base_eps=0.05, desired_reduction_ratio=0.5, max_iter=10):
    """
    Cluster candidate points adaptively so that only one representative point is kept
    if many candidates lie within a small spatial scale.
    
    Parameters:
      candidates             : NumPy array of shape (N, 3) of candidate [x, y, z] points.
      base_eps               : Initial distance threshold.
      desired_reduction_ratio: Target ratio of (number of clusters)/(number of candidates)
      max_iter               : Maximum number of iterations.
    
    Returns:
      Merged candidate points as a NumPy array of shape (M, 3), with M <= N.
    """
    if len(candidates) == 0:
        return candidates
    eps = base_eps
    for _ in range(max_iter):
        clustering = DBSCAN(eps=eps, min_samples=1).fit(candidates)
        labels = clustering.labels_
        n_clusters = len(np.unique(labels))
        ratio = n_clusters / len(candidates)
        if ratio < desired_reduction_ratio:
            break
        eps *= 1.5  # Increase eps if too many clusters remain
    # Merge points within each cluster by averaging their coordinates.
    merged = []
    for label in np.unique(labels):
        cluster_points = candidates[labels == label]
        merged.append(np.mean(cluster_points, axis=0))
    return np.array(merged)

def find_grid_critical_points(data, grad_tol=1e-3, z_threshold=None):
    """
    Identify candidate critical points on a regular grid using gradient and Hessian checks.
    Only candidates with z-values below z_threshold (if provided) are processed.
    
    Parameters:
      data       : NumPy array of shape (n^2, 3) with columns [x, y, z].
      grad_tol   : Tolerance for the gradient magnitude (only interior points below this are examined).
      z_threshold: If provided, only candidate points with z < z_threshold are considered.
      
    Returns:
      X, Y, Z   : The reshaped grid arrays.
      minima, maxima, saddles : NumPy arrays of candidate critical points (each row [x, y, z])
                                that satisfy z < z_threshold.
    """
    num_points = data.shape[0]
    n = int(np.sqrt(num_points))
    if n * n != num_points:
        raise ValueError("Data must have n^2 rows for some integer n.")
    
    # Reshape the flat data into grid arrays.
    X = data[:, 0].reshape(n, n)
    Y = data[:, 1].reshape(n, n)
    Z = data[:, 2].reshape(n, n)
    
    # Estimate grid spacing (assumed uniform).
    dx = np.mean(np.diff(X[0, :]))
    dy = np.mean(np.diff(Y[:, 0]))
    
    # Compute approximate gradients via central differences.
    # Note: For arrays created by meshgrid, axis 0 is y and axis 1 is x.
    grad_y, grad_x = np.gradient(Z, dy, dx)
    # Only consider interior points to avoid border issues.
    grad_norm = np.sqrt(grad_x[1:-1, 1:-1]**2 + grad_y[1:-1, 1:-1]**2)
    
    # Identify candidate indices in the interior where the gradient magnitude is below grad_tol.
    candidate_mask = grad_norm < grad_tol
    candidate_indices = np.argwhere(candidate_mask)
    
    minima = []
    maxima = []
    saddles = []
    
    # For each candidate, compute a finite-difference Hessian.
    for idx in candidate_indices:
        # Adjust indices to refer to the full grid.
        i, j = idx + 1
        
        # Skip candidates that do not meet the z_threshold condition.
        if z_threshold is not None and Z[i, j] >= z_threshold:
            continue
        
        # Approximate second derivatives.
        d2z_dx2 = (Z[i, j+1] - 2*Z[i, j] + Z[i, j-1]) / (dx**2)
        d2z_dy2 = (Z[i+1, j] - 2*Z[i, j] + Z[i-1, j]) / (dy**2)
        d2z_dxdy = (Z[i+1, j+1] - Z[i+1, j-1] - Z[i-1, j+1] + Z[i-1, j-1]) / (4*dx*dy)
        H = np.array([[d2z_dx2, d2z_dxdy],
                      [d2z_dxdy, d2z_dy2]])
        eigs = np.linalg.eigvals(H)
        
        pt = (X[i, j], Y[i, j], Z[i, j])
        # Classify candidate based on Hessian eigenvalues.
        if np.all(eigs > 0):
            minima.append(pt)
        elif np.all(eigs < 0):
            maxima.append(pt)
        else:
            saddles.append(pt)
    
    minima = np.array(minima) if minima else np.empty((0, 3))
    maxima = np.array(maxima) if maxima else np.empty((0, 3))
    saddles = np.array(saddles) if saddles else np.empty((0, 3))
    
    return X, Y, Z, minima, maxima, saddles

def find_grid_critical_points(data, grad_tol=1e-3, z_threshold=None, z_min=None):
    """
    Identify candidate critical points on a regular grid using gradient and Hessian checks.
    Only candidates with z-values within the range [z_min, z_threshold] (if provided) are processed.
    
    Parameters:
      data       : NumPy array of shape (n^2, 3) with columns [x, y, z].
      grad_tol   : Tolerance for the gradient magnitude (only interior points below this are examined).
      z_threshold: If provided, only candidate points with z < z_threshold are considered.
      z_min      : If provided, only candidate points with z > z_min are considered.
      
    Returns:
      X, Y, Z   : The reshaped grid arrays.
      minima, maxima, saddles : NumPy arrays of candidate critical points (each row [x, y, z])
                                that satisfy z_min < z < z_threshold.
    """
    num_points = data.shape[0]
    n = int(np.sqrt(num_points))
    if n * n != num_points:
        raise ValueError("Data must have n^2 rows for some integer n.")
    
    # Reshape the flat data into grid arrays.
    X = data[:, 0].reshape(n, n)
    Y = data[:, 1].reshape(n, n)
    Z = data[:, 2].reshape(n, n)
    
    # Estimate grid spacing (assumed uniform).
    dx = np.mean(np.diff(X[0, :]))
    dy = np.mean(np.diff(Y[:, 0]))
    
    # Compute approximate gradients via central differences.
    # Note: For arrays created by meshgrid, axis 0 is y and axis 1 is x.
    grad_y, grad_x = np.gradient(Z, dy, dx)
    # Only consider interior points to avoid border issues.
    grad_norm = np.sqrt(grad_x[1:-1, 1:-1]**2 + grad_y[1:-1, 1:-1]**2)
    
    # Identify candidate indices in the interior where the gradient magnitude is below grad_tol.
    candidate_mask = grad_norm < grad_tol
    candidate_indices = np.argwhere(candidate_mask)
    
    minima = []
    maxima = []
    saddles = []
    
    # For each candidate, compute a finite-difference Hessian.
    for idx in candidate_indices:
        # Adjust indices to refer to the full grid.
        i, j = idx + 1
        
        # Skip candidates that do not meet the z_threshold and z_min conditions.
        if z_threshold is not None and Z[i, j] >= z_threshold:
            continue
        if z_min is not None and Z[i, j] <= z_min:
            continue
        
        # Approximate second derivatives.
        d2z_dx2 = (Z[i, j+1] - 2*Z[i, j] + Z[i, j-1]) / (dx**2)
        d2z_dy2 = (Z[i+1, j] - 2*Z[i, j] + Z[i-1, j]) / (dy**2)
        d2z_dxdy = (Z[i+1, j+1] - Z[i+1, j-1] - Z[i-1, j+1] + Z[i-1, j-1]) / (4*dx*dy)
        H = np.array([[d2z_dx2, d2z_dxdy],
                      [d2z_dxdy, d2z_dy2]])
        eigs = np.linalg.eigvals(H)
        
        pt = (X[i, j], Y[i, j], Z[i, j])
        # Classify candidate based on Hessian eigenvalues.
        if np.all(eigs > 0):
            minima.append(pt)
        elif np.all(eigs < 0):
            maxima.append(pt)
        else:
            saddles.append(pt)
    
    minima = np.array(minima) if minima else np.empty((0, 3))
    maxima = np.array(maxima) if maxima else np.empty((0, 3))
    saddles = np.array(saddles) if saddles else np.empty((0, 3))
    
    return X, Y, Z, minima, maxima, saddles

def plot_grid_critical_points(data, grad_tol=1e-3, base_cluster_radius=0.05,
                              desired_reduction_ratio=0.5, z_threshold=None, z_min=None,
                              show_threshold_plane=True, show_min_plane=True):
    """
    Plot the 3D grid data along with its critical points using Plotly.
    Only candidate critical points with z_min < z < z_threshold (if provided) are computed and plotted.
    Candidate points are first identified via gradient/Hessian tests and then merged adaptively
    so that only one critical point is counted within a given distance.
    
    Data points are plotted in blue.
      - Local minima are plotted in green.
      - Local maxima are plotted in red.
      - Saddle points are plotted in purple.
    
    Parameters:
      data                  : NumPy array of shape (n^2, 3) with columns [x, y, z].
      grad_tol              : Tolerance for the gradient magnitude to consider a candidate.
      base_cluster_radius   : Initial clustering radius in the (x, y, z) space.
      desired_reduction_ratio: Target ratio for merging nearby candidates.
      z_threshold           : Upper threshold - only points with z < z_threshold are considered.
      z_min                 : Lower threshold - only points with z > z_min are considered.
      show_threshold_plane  : Whether to show the yellow threshold plane at z_threshold.
      show_min_plane        : Whether to show the red minimum plane at z_min.
    """
    X, Y, Z, minima, maxima, saddles = find_grid_critical_points(data, grad_tol=grad_tol,
                                                                z_threshold=z_threshold,
                                                                z_min=z_min)
    
    # Apply adaptive clustering to merge nearby candidate points.
    merged_minima = adaptive_cluster_candidates(minima, base_eps=base_cluster_radius,
                                              desired_reduction_ratio=desired_reduction_ratio)
    merged_maxima = adaptive_cluster_candidates(maxima, base_eps=base_cluster_radius,
                                              desired_reduction_ratio=desired_reduction_ratio)
    merged_saddles = adaptive_cluster_candidates(saddles, base_eps=base_cluster_radius,
                                               desired_reduction_ratio=desired_reduction_ratio)
    
    # Create the title with dynamic values
    z_threshold_str = str(z_threshold) if z_threshold is not None else "None"
    z_min_str = str(z_min) if z_min is not None else "None"
    title_text = f"Critical Point Analysis of Graphene Fermi Sea (Fermi Level = {z_threshold_str} eV, Min = {z_min_str} eV)"
    
    # Create Plotly traces.
    trace_data = go.Scatter3d(
        x=X.flatten(),
        y=Y.flatten(),
        z=Z.flatten(),
        mode='markers',
        marker=dict(size=1, color='blue'),
        name='Data Points'
    )
    traces = [trace_data]
    
    # Get the x and y ranges from the data for the planes
    x_min, x_max = np.min(X), np.max(X)
    y_min, y_max = np.min(Y), np.max(Y)
    
    # Create a simple 2x2 grid for the planes (only need corners)
    plane_x = np.array([[x_min, x_max], [x_min, x_max]])
    plane_y = np.array([[y_min, y_min], [y_max, y_max]])
    
    # Add a transparent yellow plane at z_threshold if specified and enabled
    if z_threshold is not None and show_threshold_plane:
        plane_z = np.ones_like(plane_x) * z_threshold
        
        # Create the threshold plane trace
        threshold_plane_trace = go.Surface(
            x=plane_x,
            y=plane_y,
            z=plane_z,
            colorscale=[[0, 'yellow'], [1, 'yellow']],  # Solid yellow
            showscale=False,  # Hide the color scale
            opacity=0.3,  # Make it transparent
            name='Fermi Level'
        )
        traces.append(threshold_plane_trace)
    
    # Add a transparent red plane at z_min if specified and enabled
    if z_min is not None and show_min_plane:
        min_plane_z = np.ones_like(plane_x) * z_min
        
        # Create the minimum plane trace
        min_plane_trace = go.Surface(
            x=plane_x,
            y=plane_y,
            z=min_plane_z,
            colorscale=[[0, 'red'], [1, 'red']],  # Solid red
            showscale=False,  # Hide the color scale
            opacity=0.3,  # Make it transparent
            name='Min Binding Energy'
        )
        traces.append(min_plane_trace)
    
    if merged_minima.size:
        trace_min = go.Scatter3d(
            x=merged_minima[:, 0],
            y=merged_minima[:, 1],
            z=merged_minima[:, 2],
            mode='markers',
            marker=dict(size=13, color='green'),
            name='Minima'
        )
        traces.append(trace_min)
    if merged_maxima.size:
        trace_max = go.Scatter3d(
            x=merged_maxima[:, 0],
            y=merged_maxima[:, 1],
            z=merged_maxima[:, 2],
            mode='markers',
            marker=dict(size=13, color='red'),
            name='Maxima'
        )
        traces.append(trace_max)
    if merged_saddles.size:
        trace_saddle = go.Scatter3d(
            x=merged_saddles[:, 0],
            y=merged_saddles[:, 1],
            z=merged_saddles[:, 2],
            mode='markers',
            marker=dict(size=13, color='purple'),
            name='Saddle Points'
        )
        traces.append(trace_saddle)
    
    layout = go.Layout(
        title=dict(
            text=title_text,
            font=dict(size=20)  # Larger title font
        ),
        scene=dict(
            xaxis=dict(
                title='k<sub>x</sub> (Å⁻¹)',
                titlefont=dict(size=16)  # Larger axis title font
            ),
            yaxis=dict(
                title='k<sub>y</sub> (Å⁻¹)',
                titlefont=dict(size=16)  # Larger axis title font
            ),
            zaxis=dict(
                title='E<sub>b</sub> (eV)',
                titlefont=dict(size=16)  # Larger axis title font
            ),
            aspectmode='cube',  # This ensures a 1:1:1 aspect ratio
            aspectratio=dict(x=1, y=1, z=1)
        ),
        legend=dict(
            font=dict(size=14)  # Larger legend font
        )
    )
    fig = go.Figure(data=traces, layout=layout)
    pyo.plot(fig)
    print(len(merged_minima)+len(merged_maxima)-len(merged_saddles), "Euler Characteristic")

if __name__ == "__main__":
    # Generate sample data using a smooth function, e.g. z = sin(x) * cos(y)
    n = 100  # grid resolution; large n is supported
    x = np.linspace(-2,  2, n)
    y = np.linspace(-2,  2, n)
    X, Y = np.meshgrid(x, y)
    a=2.46
    Z = -2.8*np.sqrt(1 + 4*np.cos(np.sqrt(3)/2 * a*Y)*np.cos(1/2 *a* X) + 4*(np.cos(1/2 *a* X))**2)
    
    # Flatten the grid to a (n^2, 3) array.
    data2 = np.column_stack((X.flatten(), Y.flatten(), Z.flatten()))
    

       # Generate sample data from a known function.
    n = 50  # grid resolution per dimension
    x = np.linspace(0, 2*np.pi, n)
    y = np.linspace(0, 2*np.pi, n)
    X, Y = np.meshgrid(x, y)
    Z = np.sin(X) * np.cos(Y)
    
    # Flatten the grid into an (n^2, 3) array.
    data = np.column_stack((X.flatten(), Y.flatten(), Z.flatten()))
    # Adjust grad_tol as needed (e.g. 0.05 might work well for this function).

    # Set a z_threshold (for example, only process critical points with z < 0).
    plot_grid_critical_points(data2, grad_tol=0.25, base_cluster_radius=0.005,
                                desired_reduction_ratio=1, z_threshold=-3.5, z_min=-100, show_min_plane=False)



data_grid2



import numpy as np
import pandas as pd
from scipy.interpolate import griddata

# 1. Load and filter the data (similar to plot_filtered_data)
loaded_data = np.load('processed_data.npy')
plot_data = pd.DataFrame(loaded_data, columns=['binding_energy', 'kx', 'ky', 'intensity'])

# Apply the same filters as in plot_filtered_data
plot_data = plot_data[plot_data['intensity'] > 0]
plot_data = plot_data[(plot_data['kx'] >= -0.1) & (plot_data['kx'] <= 0.1)]
plot_data = plot_data[(plot_data['ky'] >= -0.1) & (plot_data['ky'] <= 0.1)]
plot_data = plot_data[(plot_data['binding_energy'] >= -1.5) & (plot_data['binding_energy'] <= -0.3)]

# 2. Extract coordinates for interpolation
x_vals = plot_data['kx'].values
y_vals = plot_data['ky'].values
z_vals = plot_data['binding_energy'].values

# 3. Create a regular grid (resolution can be adjusted as needed)
n = 1000  # Number of points in each dimension
xi = np.linspace(-0.1, 0.1, n)
yi = np.linspace(-0.1, 0.1, n)
X_grid, Y_grid = np.meshgrid(xi, yi)

# 4. Interpolate filtered data onto the regular grid
Z_grid = griddata(points=(x_vals, y_vals), values=z_vals, xi=(X_grid, Y_grid), method='linear')

# 5. Handle any NaN values that might appear outside the convex hull of the data
if np.isnan(Z_grid).any():
    nan_mask = np.isnan(Z_grid)
    Z_grid[nan_mask] = griddata(points=(x_vals, y_vals), values=z_vals, 
                              xi=(X_grid[nan_mask], Y_grid[nan_mask]), method='nearest')

# 6. Format as (n², 3) array for critical point analysis
data_for_analysis = np.column_stack((X_grid.flatten(), Y_grid.flatten(), Z_grid.flatten()))

# Data is now ready for critical point analysis
# You can now use it with:
# plot_grid_critical_points(data_for_analysis, grad_tol=0.25, ...)

# Now use the data with the critical point analysis function
plot_grid_critical_points2(data_for_analysis, grad_tol=0.0025, base_cluster_radius=0.005,
                          desired_reduction_ratio=1, z_threshold=-0.3, z_min=-1)


def plot_grid_critical_points2(data, grad_tol=1e-3, base_cluster_radius=0.05,
                            desired_reduction_ratio=0.5, z_threshold=None, z_min=None,
                            show_threshold_plane=True, show_min_plane=True,
                            filter_display_by_energy=True):
    """
    Plot the 3D grid data along with its critical points using Plotly.
    Only candidate critical points with z_min < z < z_threshold (if provided) are computed and plotted.
    When filter_display_by_energy is True, also limits the displayed data points to this energy range.
    
    Parameters:
      data                  : NumPy array of shape (n^2, 3) with columns [x, y, z].
      grad_tol              : Tolerance for the gradient magnitude to consider a candidate.
      base_cluster_radius   : Initial clustering radius in the (x, y, z) space.
      desired_reduction_ratio: Target ratio for merging nearby candidates.
      z_threshold           : Upper threshold - only points with z < z_threshold are considered.
      z_min                 : Lower threshold - only points with z > z_min are considered.
      show_threshold_plane  : Whether to show the yellow threshold plane at z_threshold.
      show_min_plane        : Whether to show the red minimum plane at z_min.
      filter_display_by_energy: When True, only displays data points within the energy range.
    """
    import numpy as np
    import plotly.graph_objs as go
    import plotly.offline as pyo
    from scipy.interpolate import griddata
    
    # First, filter the data based on energy thresholds if requested
    filtered_data = data.copy()
    if filter_display_by_energy:
        if z_threshold is not None:
            filtered_data = filtered_data[filtered_data[:, 2] < z_threshold]
        if z_min is not None:
            filtered_data = filtered_data[filtered_data[:, 2] > z_min]
    
    # Get grid dimensions from the filtered data for display
    num_points = filtered_data.shape[0]
    n = int(np.sqrt(num_points))
    
    # Check if filtering resulted in irregular grid
    if n * n != num_points and filter_display_by_energy:
        # Recreate grid with filtered data
        x_vals = filtered_data[:, 0]
        y_vals = filtered_data[:, 1]
        z_vals = filtered_data[:, 2]
        
        # Determine unique x and y values
        unique_x = np.unique(x_vals)
        unique_y = np.unique(y_vals)
        n_x = len(unique_x)
        n_y = len(unique_y)
        
        # Create regular grid
        xi = np.linspace(np.min(x_vals), np.max(x_vals), 100)  # Fixed grid size for display
        yi = np.linspace(np.min(y_vals), np.max(y_vals), 100)
        X_display, Y_display = np.meshgrid(xi, yi)
        
        # Interpolate z values
        points = np.column_stack((x_vals, y_vals))
        Z_display = griddata(points, z_vals, (X_display, Y_display), method='linear')
        
        # Handle NaN values
        if np.isnan(Z_display).any():
            nan_mask = np.isnan(Z_display)
            Z_display[nan_mask] = griddata(points, z_vals, 
                                         (X_display[nan_mask], Y_display[nan_mask]), 
                                         method='nearest')
    else:
        # Use original data dimensions
        n = int(np.sqrt(data.shape[0]))
        X_display = data[:, 0].reshape(n, n)
        Y_display = data[:, 1].reshape(n, n)
        Z_display = data[:, 2].reshape(n, n)
        
        # Apply energy filtering to display data if requested
        if filter_display_by_energy:
            Z_mask = np.ones_like(Z_display, dtype=bool)
            if z_threshold is not None:
                Z_mask = Z_mask & (Z_display < z_threshold)
            if z_min is not None:
                Z_mask = Z_mask & (Z_display > z_min)
            
            # Create a copy of Z_display with filtered values
            Z_display_filtered = Z_display.copy()
            Z_display_filtered[~Z_mask] = np.nan
            Z_display = Z_display_filtered
    
    # Find critical points - always using the energy-filtered data
    data_for_analysis = data.copy()
    if z_threshold is not None:
        data_for_analysis = data_for_analysis[data_for_analysis[:, 2] < z_threshold]
    if z_min is not None:
        data_for_analysis = data_for_analysis[data_for_analysis[:, 2] > z_min]
    
    # Recreate grid for analysis if needed
    if data_for_analysis.shape[0] != data.shape[0]:
        # Create a grid for analysis similar to the display grid creation
        x_vals = data_for_analysis[:, 0]
        y_vals = data_for_analysis[:, 1]
        z_vals = data_for_analysis[:, 2]
        
        # Create regular grid for analysis
        n_analysis = 400  # Analysis resolution
        xi = np.linspace(np.min(x_vals), np.max(x_vals), n_analysis)
        yi = np.linspace(np.min(y_vals), np.max(y_vals), n_analysis)
        X_analysis, Y_analysis = np.meshgrid(xi, yi)
        Z_analysis = griddata(np.column_stack((x_vals, y_vals)), z_vals, (X_analysis, Y_analysis), method='linear')
        
        # Handle NaN values
        if np.isnan(Z_analysis).any():
            nan_mask = np.isnan(Z_analysis)
            Z_analysis[nan_mask] = griddata(np.column_stack((x_vals, y_vals)), z_vals, 
                                          (X_analysis[nan_mask], Y_analysis[nan_mask]), 
                                          method='nearest')
        
        # Format data for critical point analysis
        critical_data = np.column_stack((X_analysis.flatten(), Y_analysis.flatten(), Z_analysis.flatten()))
        X, Y, Z, minima, maxima, saddles = find_grid_critical_points(critical_data, grad_tol=grad_tol)
    else:
        # Use original find_grid_critical_points function as before
        X, Y, Z, minima, maxima, saddles = find_grid_critical_points(data_for_analysis, grad_tol=grad_tol)
    
    # Apply adaptive clustering to merge nearby candidate points
    merged_minima = adaptive_cluster_candidates(minima, base_eps=base_cluster_radius,
                                              desired_reduction_ratio=desired_reduction_ratio)
    merged_maxima = adaptive_cluster_candidates(maxima, base_eps=base_cluster_radius,
                                              desired_reduction_ratio=desired_reduction_ratio)
    merged_saddles = adaptive_cluster_candidates(saddles, base_eps=base_cluster_radius,
                                               desired_reduction_ratio=desired_reduction_ratio)
    
    # Create the title with dynamic values
    z_threshold_str = str(z_threshold) if z_threshold is not None else "None"
    z_min_str = str(z_min) if z_min is not None else "None"
    title_text = f"Critical Point Analysis of Graphene Fermi Sea (Fermi Level = {z_threshold_str} eV, Min = {z_min_str} eV)"
    
    # Create Plotly traces for the display data
    if filter_display_by_energy:
        # Only show filtered data
        trace_data = go.Scatter3d(
            x=X_display.flatten(),
            y=Y_display.flatten(),
            z=Z_display.flatten(),
            mode='markers',
            marker=dict(size=1, color='blue'),
            name='Data Points'
        )
    else:
        # Show all data but highlight filtered region
        in_range = np.ones(data.shape[0], dtype=bool)
        if z_threshold is not None:
            in_range &= (data[:, 2] < z_threshold)
        if z_min is not None:
            in_range &= (data[:, 2] > z_min)
            
        colors = np.where(in_range, 'blue', 'lightgray')
        
        trace_data = go.Scatter3d(
            x=data[:, 0],
            y=data[:, 1],
            z=data[:, 2],
            mode='markers',
            marker=dict(
                size=1, 
                color=colors
            ),
            name='Data Points'
        )
    
    traces = [trace_data]
    
    # Get the x and y ranges from the data for the planes
    x_min, x_max = np.min(X), np.max(X)
    y_min, y_max = np.min(Y), np.max(Y)
    
    # Create a simple 2x2 grid for the planes (only need corners)
    plane_x = np.array([[x_min, x_max], [x_min, x_max]])
    plane_y = np.array([[y_min, y_min], [y_max, y_max]])
    
    # Add a transparent yellow plane at z_threshold if specified and enabled
    if z_threshold is not None and show_threshold_plane:
        plane_z = np.ones_like(plane_x) * z_threshold
        threshold_plane_trace = go.Surface(
            x=plane_x, y=plane_y, z=plane_z,
            colorscale=[[0, 'yellow'], [1, 'yellow']],
            showscale=False, opacity=0.3, name='Fermi Level'
        )
        traces.append(threshold_plane_trace)
    
    # Add a transparent red plane at z_min if specified and enabled
    if z_min is not None and show_min_plane:
        min_plane_z = np.ones_like(plane_x) * z_min
        min_plane_trace = go.Surface(
            x=plane_x, y=plane_y, z=min_plane_z,
            colorscale=[[0, 'red'], [1, 'red']],
            showscale=False, opacity=0.3, name='Min Binding Energy'
        )
        traces.append(min_plane_trace)
    
    # Add critical points to the plot
    if merged_minima.size:
        traces.append(go.Scatter3d(
            x=merged_minima[:, 0], y=merged_minima[:, 1], z=merged_minima[:, 2],
            mode='markers', marker=dict(size=7, color='green'), name='Minima'
        ))
    if merged_maxima.size:
        traces.append(go.Scatter3d(
            x=merged_maxima[:, 0], y=merged_maxima[:, 1], z=merged_maxima[:, 2],
            mode='markers', marker=dict(size=7, color='red'), name='Maxima'
        ))
    if merged_saddles.size:
        traces.append(go.Scatter3d(
            x=merged_saddles[:, 0], y=merged_saddles[:, 1], z=merged_saddles[:, 2],
            mode='markers', marker=dict(size=7, color='purple'), name='Saddle Points'
        ))
    
    # Layout and figure creation
    layout = go.Layout(
        title=dict(text=title_text, font=dict(size=20)),
        scene=dict(
            xaxis=dict(title='k<sub>x</sub> (Å⁻¹)', titlefont=dict(size=16)),
            yaxis=dict(title='k<sub>y</sub> (Å⁻¹)', titlefont=dict(size=16)),
            zaxis=dict(title='E<sub>b</sub> (eV)', titlefont=dict(size=16)),
            aspectmode='cube',
            aspectratio=dict(x=1, y=1, z=1)
        ),
        legend=dict(font=dict(size=14))
    )
    fig = go.Figure(data=traces, layout=layout)
    pyo.plot(fig)
    print(len(merged_minima)+len(merged_maxima)-len(merged_saddles), "Euler Characteristic")


