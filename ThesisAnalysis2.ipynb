{"cells": [{"cell_type": "code", "execution_count": null, "id": "622bef37", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from arpes.load_pxt import read_single_pxt\n", "import tkinter as tk\n", "from tkinter import filedialog, messagebox\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from arpes.load_pxt import read_single_pxt\n", "import tkinter as tk\n", "from tkinter import filedialog, messagebox\n", "import os\n", "import numpy as np\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "import matplotlib.collections as mcollections\n", "from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox\n", "from matplotlib.colors import Normalize\n", "from IPython.display import display\n", "import tkinter as tk\n", "from tkinter import filedialog\n", "from arpes.load_pxt import read_single_pxt\n", "import warnings\n", "from matplotlib import MatplotlibDeprecationWarning\n", "from skimage.feature import canny\n", "from matplotlib.colors import ListedColormap\n", "\n", "igor_data = np.array([\n", "    [57600, 54784, 58112],\n", "    [56561.95, 53415.66, 57121.13],\n", "    [55523.89, 52047.31, 56130.26],\n", "    [54485.84, 50678.96, 55139.39],\n", "    [53447.78, 49310.62, 54148.52],\n", "    [52409.73, 47942.27, 53157.65],\n", "    [51371.67, 46655.25, 52193.88],\n", "    [50333.62, 45428.45, 51250.2],\n", "    [49295.56, 44201.66, 50306.51],\n", "    [48257.51, 42974.87, 49362.82],\n", "    [47219.45, 41748.08, 48419.14],\n", "    [46223.56, 40563.45, 47510.59],\n", "    [45468.61, 39619.77, 46802.82],\n", "    [44713.66, 38676.08, 46095.06],\n", "    [43958.71, 37732.39, 45387.29],\n", "    [43203.77, 36788.71, 44679.53],\n", "    [42448.82, 35845.02, 43971.77],\n", "    [41693.87, 34833.07, 43195.73],\n", "    [40938.92, 33795.01, 42393.6],\n", "    [40183.97, 32756.96, 41591.46],\n", "    [39429.02, 31718.9, 40789.33],\n", "    [38674.07, 30680.85, 39987.2],\n", "    [37919.12, 29670.9, 39171.01],\n", "    [37164.17, 28727.21, 38321.7],\n", "    [36409.22, 27783.53, 37472.38],\n", "    [35654.27, 26839.84, 36623.06],\n", "    [34899.32, 25896.16, 35773.74],\n", "    [34144.38, 24952.47, 34924.42],\n", "    [33512.91, 24173.43, 34239.75],\n", "    [32899.52, 23418.48, 33579.17],\n", "    [32286.12, 22663.53, 32918.59],\n", "    [31672.72, 21908.58, 32258.01],\n", "    [31059.33, 21153.63, 31597.43],\n", "    [30467.01, 20419.77, 30957.93],\n", "    [29900.8, 19712, 30344.53],\n", "    [29334.59, 19004.23, 29731.14],\n", "    [28768.38, 18296.47, 29117.74],\n", "    [28202.16, 17588.71, 28504.35],\n", "    [27641.98, 16886.96, 27901.99],\n", "    [27358.87, 16462.31, 27807.62],\n", "    [27075.77, 16037.65, 27713.26],\n", "    [26792.66, 15612.99, 27618.89],\n", "    [26509.55, 15188.33, 27524.52],\n", "    [26226.45, 14763.67, 27430.15],\n", "    [26027.67, 14479.56, 27448.22],\n", "    [25886.12, 14290.82, 27542.59],\n", "    [25744.56, 14102.09, 27636.96],\n", "    [25603.01, 13913.35, 27731.33],\n", "    [25461.46, 13724.61, 27825.69],\n", "    [25279.75, 13503.75, 27944.16],\n", "    [24902.28, 13126.27, 28180.08],\n", "    [24524.8, 12748.8, 28416],\n", "    [24147.33, 12371.33, 28651.92],\n", "    [23769.85, 11993.85, 28887.84],\n", "    [23392.38, 11616.38, 29123.77],\n", "    [22874.35, 11168.63, 29359.69],\n", "    [22308.14, 10696.78, 29595.61],\n", "    [21741.93, 10224.94, 29831.53],\n", "    [21175.72, 9753.098, 30067.45],\n", "    [20609.51, 9281.255, 30303.37],\n", "    [19952.94, 8899.765, 30539.29],\n", "    [19103.62, 8711.027, 30775.21],\n", "    [18254.31, 8522.29, 31011.14],\n", "    [17404.99, 8333.553, 31247.06],\n", "    [16555.67, 8144.816, 31482.98],\n", "    [15706.35, 7956.079, 31718.9],\n", "    [14688.38, 7893.835, 31828.33],\n", "    [13650.32, 7846.651, 31922.7],\n", "    [12612.27, 7799.467, 32017.07],\n", "    [11574.21, 7752.282, 32111.44],\n", "    [10536.16, 7705.098, 32205.8],\n", "    [9807.31, 7922.949, 32388.52],\n", "    [9429.835, 8441.977, 32671.62],\n", "    [9052.36, 8961.004, 32954.73],\n", "    [8674.887, 9480.031, 33237.84],\n", "    [8297.412, 9999.059, 33520.94],\n", "    [7911.906, 10526.12, 33812.08],\n", "    [7345.694, 11233.88, 34283.92],\n", "    [6779.482, 11941.65, 34755.77],\n", "    [6213.271, 12649.41, 35227.61],\n", "    [5647.059, 13357.18, 35699.45],\n", "    [5080.847, 14064.94, 36171.29],\n", "    [4543.749, 14714.48, 36614.02],\n", "    [4024.722, 15327.87, 37038.68],\n", "    [3505.694, 15941.27, 37463.34],\n", "    [2986.667, 16554.67, 37888],\n", "    [2467.639, 17168.06, 38312.66],\n", "    [1984.753, 17790.49, 38764.42],\n", "    [1654.463, 18451.07, 39330.64],\n", "    [1324.173, 19111.65, 39896.85],\n", "    [993.8823, 19772.23, 40463.06],\n", "    [663.5922, 20432.82, 41029.27],\n", "    [333.302, 21093.4, 41595.48],\n", "    [256, 21464.85, 41944.85],\n", "    [256, 21747.95, 42227.95],\n", "    [256, 22031.06, 42511.06],\n", "    [256, 22314.16, 42794.16],\n", "    [256, 22597.27, 43077.27],\n", "    [239.9373, 23008.88, 43456.75],\n", "    [192.7529, 23669.46, 44022.96],\n", "    [145.5686, 24330.04, 44589.18],\n", "    [98.38432, 24990.62, 45155.39],\n", "    [51.2, 25651.2, 45721.6],\n", "    [4.015687, 26311.78, 46287.81],\n", "    [0, 26972.36, 46897.19],\n", "    [0, 27632.94, 47510.59],\n", "    [0, 28293.52, 48123.98],\n", "    [0, 28954.1, 48737.38],\n", "    [0, 29614.68, 49350.78],\n", "    [0, 30344.53, 50033.44],\n", "    [0, 31146.67, 50788.39],\n", "    [0, 31948.8, 51543.34],\n", "    [0, 32750.93, 52298.29],\n", "    [0, 33553.07, 53053.24],\n", "    [0, 34358.21, 53805.18],\n", "    [0, 35207.53, 54512.94],\n", "    [0, 36056.85, 55220.71],\n", "    [0, 36906.16, 55928.47],\n", "    [0, 37755.48, 56636.23],\n", "    [0, 38604.8, 57344],\n", "    [0, 39062.59, 57208.47],\n", "    [0, 39298.51, 56595.07],\n", "    [0, 39534.43, 55981.68],\n", "    [0, 39770.35, 55368.28],\n", "    [0, 40006.27, 54754.89],\n", "    [0, 40181.96, 54041.1],\n", "    [0, 40134.78, 52955.86],\n", "    [0, 40087.59, 51870.62],\n", "    [0, 40040.41, 50785.38],\n", "    [0, 39993.22, 49700.14],\n", "    [0, 39946.04, 48614.9],\n", "    [0, 39936, 47641.1],\n", "    [0, 39936, 46697.41],\n", "    [0, 39936, 45753.73],\n", "    [0, 39936, 44810.04],\n", "    [0, 39936, 43866.35],\n", "    [0, 39918.93, 42854.4],\n", "    [0, 39871.75, 41721.98],\n", "    [0, 39824.57, 40589.55],\n", "    [0, 39777.38, 39457.13],\n", "    [0, 39730.2, 38324.71],\n", "    [0, 39683.01, 37192.28],\n", "    [0, 39680, 36369.07],\n", "    [0, 39680, 35566.93],\n", "    [0, 39680, 34764.8],\n", "    [0, 39680, 33962.67],\n", "    [0, 39680, 33160.54],\n", "    [0, 39680, 32527.06],\n", "    [0, 39680, 32055.21],\n", "    [0, 39680, 31583.37],\n", "    [0, 39680, 31111.53],\n", "    [0, 39680, 30639.69],\n", "    [0, 39675.98, 30123.67],\n", "    [0, 39628.8, 29132.8],\n", "    [0, 39581.62, 28141.93],\n", "    [0, 39534.43, 27151.06],\n", "    [0, 39487.25, 26160.19],\n", "    [0, 39440.06, 25169.32],\n", "    [0, 39361.76, 24240.69],\n", "    [0, 39267.39, 23344.19],\n", "    [0, 39173.02, 22447.69],\n", "    [0, 39078.65, 21551.18],\n", "    [0, 38984.28, 20654.68],\n", "    [0, 38923.04, 19835.48],\n", "    [0, 38970.23, 19269.27],\n", "    [0, 39017.41, 18703.06],\n", "    [0, 39064.6, 18136.85],\n", "    [0, 39111.78, 17570.63],\n", "    [0, 39158.96, 17004.42],\n", "    [0, 39435.04, 16781.55],\n", "    [0, 39765.33, 16640],\n", "    [0, 40095.62, 16498.45],\n", "    [0, 40425.91, 16356.89],\n", "    [0, 40756.2, 16215.34],\n", "    [993.8823, 41122.64, 16073.79],\n", "    [3589.02, 41547.29, 15932.24],\n", "    [6184.157, 41971.95, 15790.68],\n", "    [8779.294, 42396.61, 15649.13],\n", "    [11374.43, 42821.27, 15507.58],\n", "    [13969.57, 43245.93, 15366.02],\n", "    [15796.71, 43715.77, 15224.47],\n", "    [17589.71, 44187.61, 15082.92],\n", "    [19382.71, 44659.45, 14941.36],\n", "    [21175.72, 45131.29, 14799.81],\n", "    [22968.72, 45603.14, 14658.26],\n", "    [24686.43, 46100.08, 14516.71],\n", "    [26337.88, 46619.11, 14375.15],\n", "    [27989.33, 47138.13, 14233.6],\n", "    [29640.79, 47657.16, 14092.05],\n", "    [31292.23, 48176.19, 13950.49],\n", "    [32933.65, 48705.25, 13798.9],\n", "    [34490.73, 49318.65, 13562.98],\n", "    [36047.81, 49932.05, 13327.06],\n", "    [37604.89, 50545.44, 13091.14],\n", "    [39161.98, 51158.84, 12855.22],\n", "    [40719.06, 51772.23, 12619.29],\n", "    [41922.76, 52225, 12415.5],\n", "    [42960.82, 52602.48, 12226.76],\n", "    [43998.87, 52979.95, 12038.02],\n", "    [45036.93, 53357.43, 11849.29],\n", "    [46074.98, 53734.9, 11660.55],\n", "    [47293.74, 54196.71, 11411.58],\n", "    [49039.56, 54904.47, 10986.92],\n", "    [50785.38, 55612.23, 10562.26],\n", "    [52531.2, 56320, 10137.6],\n", "    [54277.02, 57027.77, 9712.941],\n", "    [56022.84, 57735.53, 9288.282],\n", "    [57494.59, 58325.84, 8785.317],\n", "    [58910.12, 58892.05, 8266.29],\n", "    [60325.65, 59458.26, 7747.263],\n", "    [61741.18, 60024.47, 7228.235],\n", "    [63156.71, 60590.68, 6709.208],\n", "    [64076.3, 60470.21, 6457.224],\n", "    [64265.04, 59337.79, 6598.776],\n", "    [64453.77, 58205.36, 6740.33],\n", "    [64642.51, 57072.94, 6881.882],\n", "    [64831.25, 55940.52, 7023.435],\n", "    [65019.98, 54808.09, 7164.988],\n", "    [64746.92, 53260.05, 7398.902],\n", "    [64463.81, 51702.96, 7634.824],\n", "    [64180.71, 50145.88, 7870.745],\n", "    [63897.6, 48588.8, 8106.667],\n", "    [63614.49, 47031.72, 8342.588],\n", "    [63592.41, 45605.14, 8474.102],\n", "    [63781.14, 44283.98, 8521.286],\n", "    [63969.88, 42962.82, 8568.471],\n", "    [64158.62, 41641.66, 8615.655],\n", "    [64347.36, 40320.5, 8662.839],\n", "    [64415.62, 38993.32, 8704],\n", "    [63660.68, 37624.97, 8704],\n", "    [62905.73, 36256.63, 8704],\n", "    [62150.78, 34888.28, 8704],\n", "    [61395.83, 33519.94, 8704],\n", "    [60640.88, 32151.59, 8704],\n", "    [60283.48, 30882.63, 8704],\n", "    [60094.75, 29655.84, 8704],\n", "    [59906.01, 28429.05, 8704],\n", "    [59717.27, 27202.26, 8704],\n", "    [59528.54, 25975.47, 8704],\n", "    [59339.8, 24722.57, 8704],\n", "    [59151.06, 23401.41, 8704],\n", "    [58962.32, 22080.25, 8704],\n", "    [58773.59, 20759.09, 8704],\n", "    [58584.85, 19437.93, 8704],\n", "    [58396.11, 18116.77, 8704],\n", "    [58287.69, 17197.18, 8704],\n", "    [58193.32, 16347.86, 8704],\n", "    [58098.95, 15498.54, 8704],\n", "    [58004.58, 14649.22, 8704],\n", "    [57910.21, 13799.91, 8704],\n", "    [57795.77, 12267.92, 8704],\n", "    [57654.21, 9814.337, 8704],\n", "    [57512.66, 7360.753, 8704],\n", "    [57371.11, 4907.168, 8704],\n", "    [57229.55, 2453.584, 8704],\n", "    [57088, 0, 8704]])\n", "# Create the array from the provided data\n", "\n", "\n", "# Normalize the RGB values to the range 0-1\n", "normalized_data = igor_data / 65535.0\n", "\n", "# Create the ListedColormap\n", "rainbowlightct = ListedColormap(normalized_data)\n", "rainbowlightctplotly = [f'rgb({int(r*255)},{int(g*255)},{int(b*255)})' for r,g,b in normalized_data]\n", "warnings.filterwarnings(\"ignore\", category=UserWarning)\n", "if not hasattr(np, 'complex'):\n", "    np.complex = np.complex128\n", "\n", "\n", "def load_pxt_files():\n", "    folder_path = filedialog.askdirectory(title=\"Select folder containing PXT files\")\n", "    if not folder_path:\n", "        return\n", "\n", "    pxt_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "    if not pxt_files:\n", "        messagebox.showinfo(\"No PXT files\", \"No PXT files found in the selected folder.\")\n", "        return\n", "\n", "    pxt_files.sort()\n", "    data_arrays = []\n", "    attributes = []\n", "    \n", "    for file in pxt_files:\n", "        file_path = os.path.join(folder_path, file)\n", "        try:\n", "            data = read_single_pxt(file_path)\n", "            df = pd.DataFrame(data.values, \n", "                            columns=data.coords['phi'].values,\n", "                            index=data.coords['eV'].values)\n", "            data_arrays.append(df)\n", "            attributes.append(data.attrs)\n", "        except Exception as e:\n", "            messagebox.showerror(\"Error\", f\"Failed to load {file}: {str(e)}\")\n", "\n", "    messagebox.showinfo(\"Success\", f\"Loaded {len(data_arrays)} PXT files.\")\n", "    return data_arrays, attributes\n", "\n", "# GUI setup\n", "root = tk.Tk()\n", "root.title(\"ARPES Data Processor\")\n", "\n", "def load_and_process():\n", "    global data_proc, data_EK, data_attributes\n", "    data_raw, data_attributes = load_pxt_files()\n", "    \n", "    # Clear previous data\n", "    \n", "    # Convert to binding energy\n", "    work_function = 4.5  # eV\n", "    data_proc = []\n", "    data_EK = []\n", "    for i, df in enumerate(data_raw):\n", "        hv = data_attributes[i]['hv']\n", "        new_index = hv - work_function - df.index.values\n", "        proc_df = df.set_index(pd.Index(new_index))\n", "        data_proc.append(proc_df)\n", "    \n", "    # Convert to E-kx-ky space\n", "    for i, df in enumerate(data_proc):\n", "        hv = data_attributes[i]['hv']\n", "        polar = np.deg2rad(data_attributes[i]['polar'])\n", "        \n", "        # Get dimensions\n", "        n_energy = len(df.index)\n", "        n_angles = len(df.columns)\n", "        \n", "        # Create meshgrids for proper broadcasting\n", "        E_binding = df.index.values[:, np.newaxis]  # Shape (n_energy, 1)\n", "        theta = np.deg2rad(df.columns.values)        # Shape (n_angles,)\n", "        \n", "        # Broadcast to 2D grids\n", "        E_binding_grid = np.tile(E_binding, (1, n_angles))  # Shape (n_energy, n_angles)\n", "        theta_grid = np.tile(theta, (n_energy, 1))          # Shape (n_energy, n_angles)\n", "        \n", "        # Calculate momentum components\n", "        E_kinetic = hv - work_function - E_binding_grid\n", "        k_mag = 0.5123 * np.sqrt(E_binding_grid)\n", "        \n", "        kx = k_mag * np.sin(theta_grid)\n", "        ky = k_mag * np.sin(polar)\n", "        \n", "        # Create DataFrame with equal-length arrays\n", "        scan_data = {\n", "            'E_binding': -E_kinetic.flatten(),\n", "            'kx': kx.flatten(),\n", "            'ky': ky.flatten(),\n", "            'intensity': df.values.flatten()\n", "        }\n", "        \n", "        data_EK.append(pd.DataFrame(scan_data))\n", "        \n", "load_button = tk.<PERSON><PERSON>(root, text=\"Load and Process Data\", command=load_and_process)\n", "load_button.pack(pady=20)\n", "root.mainloop()\n"]}, {"cell_type": "code", "execution_count": null, "id": "c323ccf8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import plotly.express as px\n", "\n", "def process_threshold_and_filter(data, threshold, window_size=3, \n", "                                min_periods=1, suppress_at='max'):\n", "    # Convert input data to DataFrame\n", "    if isinstance(data, (list, np.ndarray)):\n", "        data_arr = np.asarray(data)\n", "        df = pd.DataFrame(data_arr.reshape(-1, data_arr.shape[-1]),\n", "                         columns=['binding_energy', 'kx', 'ky', 'intensity'])\n", "    else:\n", "        df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])\n", "\n", "    # Clean data if stored as lists\n", "    if df.applymap(lambda x: isinstance(x, list)).any().any():\n", "        df = df.applymap(lambda y: y[0] if isinstance(y, list) else y)\n", "\n", "     # Normalize and threshold\n", "    df['intensity'] = df.groupby('binding_energy')['intensity'].transform(\n", "        lambda x: x/x.max() if x.max() > 0 else 0)\n", "    df.loc[df['intensity'] < threshold, 'intensity'] = 0\n", "\n", "    # Apply processing\n", "    df['intensity'] = df.groupby('binding_energy')['intensity'].transform(\n", "        lambda x: x.rolling(window_size, min_periods=min_periods, \n", "                           center=True).mean().fillna(0))\n", "    \n", "    # Normalize and threshold\n", "    df['intensity'] = df.groupby('binding_energy')['intensity'].transform(\n", "        lambda x: x/x.max() if x.max() > 0 else 0)\n", "    df.loc[df['intensity'] < threshold, 'intensity'] = 0\n", "\n", "    # Create a mask for non-zero intensities\n", "    nonzero_mask = df['intensity'] > 0\n", "\n", "    # Group by kx, ky coordinates\n", "    coords_group = df[nonzero_mask].groupby(['kx', 'ky'])\n", "\n", "    # Find the binding energy to keep for each coordinate\n", "    if suppress_at == 'max':\n", "        energy_to_keep_sub = coords_group['binding_energy'].transform('max')\n", "    elif suppress_at == 'min':\n", "        energy_to_keep_sub = coords_group['binding_energy'].transform('min')\n", "    elif suppress_at == 'none':\n", "        # Return processed data without suppression\n", "        processed_data = df.set_index(['binding_energy', 'kx', 'ky'])\n", "        np.save('processed_data.npy', processed_data.reset_index().to_numpy())\n", "        return processed_data\n", "    else:\n", "        energy_to_keep_sub = coords_group['binding_energy'].transform('min')\n", "\n", "    # Reindex the result from the non-zero subset to the full DataFrame index\n", "    energy_to_keep = pd.Series(np.nan, index=df.index)\n", "    energy_to_keep.loc[nonzero_mask] = energy_to_keep_sub\n", "\n", "    # Create a boolean mask for points to keep\n", "    keep_mask = (df['binding_energy'] == energy_to_keep) & nonzero_mask\n", "\n", "    # Set intensity to 0 for all points except those we want to keep\n", "    df.loc[~keep_mask, 'intensity'] = 0\n", "\n", "    # Return processed data\n", "    processed_data = df.set_index(['binding_energy', 'kx', 'ky'])\n", "    np.save('processed_data.npy', processed_data.reset_index().to_numpy())\n", "    \n", "    return processed_data\n", "\n", "def plot_filtered_data(filename='processed_data.npy', downsample_fraction=0.1, min_energy=None, max_energy=None, point_size=1):\n", "    # Load data from numpy file\n", "    loaded_data = np.load(filename)\n", "    plot_data = pd.DataFrame(loaded_data, columns=['binding_energy', 'kx', 'ky', 'intensity'])\n", "    \n", "    # Filter nonzero points\n", "    plot_data = plot_data[plot_data['intensity'] > 0]\n", "    \n", "    # Filter by energy range if specified\n", "    if min_energy is not None:\n", "        plot_data = plot_data[plot_data['binding_energy'] >= min_energy]\n", "    if max_energy is not None:\n", "        plot_data = plot_data[plot_data['binding_energy'] <= max_energy]\n", "    \n", "    if downsample_fraction < 1 and len(plot_data) > 0:\n", "        plot_data = plot_data.sample(frac=downsample_fraction)\n", "    \n", "    fig = px.scatter_3d(plot_data, \n", "                        x='kx', \n", "                        y='ky', \n", "                        z='binding_energy',\n", "                        color='intensity',\n", "                        color_continuous_scale='viridis',\n", "                        range_color=[0, 1],  \n", "                        labels={\n", "                            'kx': 'kx',\n", "                            'ky': 'ky',\n", "                            'binding_energy': 'Binding Energy',\n", "                            'intensity': 'Normalized Intensity'\n", "                        })\n", "    \n", "    # Make points smaller\n", "    fig.update_traces(marker=dict(size=point_size))\n", "    fig.show()\n", "\n", "# Call the functions\n", "data_processed = process_threshold_and_filter(data_EK, 0.10, window_size=10, min_periods=1, suppress_at='none')"]}, {"cell_type": "code", "execution_count": null, "id": "a257ce20", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import plotly.graph_objects as go\n", "from scipy.interpolate import griddata\n", "from scipy.ndimage import gaussian_filter\n", "import time\n", "\n", "def find_critical_points(filename='processed_data.npy', energy_step=0.01, \n", "                         grid_size=100, intensity_threshold=0.01,\n", "                         smoothing_sigma=1.0, gradient_threshold=1e-5,\n", "                         kx_range=None, ky_range=None, energy_range=None,\n", "                         kx_offset=0.0, ky_offset=0.0, energy_offset=0.0):\n", "    \"\"\"\n", "    Find and classify critical points on the ARPES data surface.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the processed_data.npy file\n", "    energy_step : float\n", "        Step size for binding energy slices\n", "    grid_size : int\n", "        Size of the interpolation grid\n", "    intensity_threshold : float\n", "        Minimum intensity value to consider\n", "    smoothing_sigma : float\n", "        Sigma parameter for Gaussian smoothing\n", "    gradient_threshold : float\n", "        <PERSON><PERSON><PERSON><PERSON> for considering a point as a critical point\n", "    kx_range : tuple or None\n", "        (min_kx, max_kx) range to include\n", "    ky_range : tuple or None\n", "        (min_ky, max_ky) range to include\n", "    energy_range : tuple or None\n", "        (min_energy, max_energy) range to include\n", "    kx_offset : float\n", "        Offset to apply to kx values in results\n", "    ky_offset : float\n", "        Offset to apply to ky values in results\n", "    energy_offset : float\n", "        Offset to apply to binding energy values in results\n", "    \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary containing critical points and their classification\n", "    \"\"\"\n", "    print(\"Loading and processing data...\")\n", "    start_time = time.time()\n", "    \n", "    # Load data\n", "    data = np.load(filename)\n", "    df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])\n", "    \n", "    # Apply filters\n", "    df = df[df['intensity'] > intensity_threshold]\n", "    \n", "    if kx_range is not None:\n", "        df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]\n", "    \n", "    if ky_range is not None:\n", "        df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]\n", "    \n", "    if energy_range is not None:\n", "        df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]\n", "    \n", "    # Define grid limits\n", "    if kx_range is not None:\n", "        kx_min, kx_max = kx_range\n", "    else:\n", "        kx_min, kx_max = df['kx'].min(), df['kx'].max()\n", "    \n", "    if ky_range is not None:\n", "        ky_min, ky_max = ky_range\n", "    else:\n", "        ky_min, ky_max = df['ky'].min(), df['ky'].max()\n", "    \n", "    if energy_range is not None:\n", "        min_energy, max_energy = energy_range\n", "    else:\n", "        min_energy = df['binding_energy'].min()\n", "        max_energy = df['binding_energy'].max()\n", "    \n", "    # Create grid arrays\n", "    energy_values = np.arange(min_energy, max_energy, energy_step)\n", "    if max_energy - energy_values[-1] > 0.01 * energy_step:\n", "        energy_values = np.append(energy_values, max_energy)\n", "    \n", "    kx_grid = np.linspace(kx_min, kx_max, grid_size)\n", "    ky_grid = np.linspace(ky_min, ky_max, grid_size)\n", "    KX, KY = np.meshgrid(kx_grid, ky_grid)\n", "    \n", "    # Build 3D intensity array\n", "    intensity_grid = np.zeros((len(energy_values), grid_size, grid_size))\n", "    \n", "    print(f\"Processing {len(energy_values)} energy slices...\")\n", "    \n", "    # Interpolate intensity for each energy slice\n", "    for i, energy in enumerate(energy_values):\n", "        if i % 10 == 0:\n", "            print(f\"Processing slice {i}/{len(energy_values)}, energy={energy:.3f}\")\n", "        \n", "        # Filter data for current energy bin\n", "        if i == len(energy_values) - 1:\n", "            slice_data = df[(df['binding_energy'] >= energy) & \n", "                          (df['binding_energy'] <= max_energy)]\n", "        else:\n", "            slice_data = df[(df['binding_energy'] >= energy) & \n", "                          (df['binding_energy'] < energy_values[i+1])]\n", "        \n", "        if len(slice_data) > 3:\n", "            intensity_values = griddata(\n", "                (slice_data['kx'], slice_data['ky']), \n", "                slice_data['intensity'],\n", "                (KX, KY),\n", "                method='linear',\n", "                fill_value=0\n", "            )\n", "            \n", "            intensity_grid[i] = intensity_values\n", "    \n", "    # Apply Gaussian smoothing to intensity grid\n", "    print(\"Applying smoothing to intensity data...\")\n", "    smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)\n", "    \n", "    # Compute numerical gradients\n", "    print(\"Computing gradients for critical point detection...\")\n", "    grad_e, grad_ky, grad_kx = np.gradient(smoothed_intensity)\n", "    \n", "    # Find potential critical points\n", "    print(\"Finding critical points...\")\n", "    # Points where all gradient components are close to zero\n", "    critical_mask = (np.abs(grad_e) < gradient_threshold) & \\\n", "                    (np.abs(grad_ky) < gradient_threshold) & \\\n", "                    (np.abs(grad_kx) < gradient_threshold)\n", "    \n", "    # Get coordinates of critical points\n", "    e_indices, ky_indices, kx_indices = np.where(critical_mask)\n", "    \n", "    print(f\"Found {len(e_indices)} potential critical points\")\n", "    \n", "    # Analyze critical points with Hessian matrix\n", "    print(\"Classifying critical points using Hessian matrices...\")\n", "    \n", "    minima = []\n", "    maxima = []\n", "    saddle_points = []\n", "    \n", "    for i in range(len(e_indices)):\n", "        # Skip points at the boundary of the grid\n", "        if (e_indices[i] == 0 or e_indices[i] == len(energy_values)-1 or\n", "            ky_indices[i] == 0 or ky_indices[i] == grid_size-1 or\n", "            kx_indices[i] == 0 or kx_indices[i] == grid_size-1):\n", "            continue\n", "        \n", "        # Compute Hessian matrix\n", "        hessian = np.zeros((3, 3))\n", "        \n", "        # Second derivatives\n", "        hessian[0, 0] = (smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]] - \n", "                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + \n", "                          smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]])\n", "        \n", "        hessian[1, 1] = (smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]] - \n", "                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + \n", "                          smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]])\n", "        \n", "        hessian[2, 2] = (smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]+1] - \n", "                          2*smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]] + \n", "                          smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]-1])\n", "        \n", "        # Mixed derivatives\n", "        hessian[0, 1] = hessian[1, 0] = (\n", "            (smoothed_intensity[e_indices[i]+1, ky_indices[i]+1, kx_indices[i]] - \n", "             smoothed_intensity[e_indices[i]+1, ky_indices[i]-1, kx_indices[i]] - \n", "             smoothed_intensity[e_indices[i]-1, ky_indices[i]+1, kx_indices[i]] + \n", "             smoothed_intensity[e_indices[i]-1, ky_indices[i]-1, kx_indices[i]]) / 4.0\n", "        )\n", "        \n", "        hessian[0, 2] = hessian[2, 0] = (\n", "            (smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]+1] - \n", "             smoothed_intensity[e_indices[i]+1, ky_indices[i], kx_indices[i]-1] - \n", "             smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]+1] + \n", "             smoothed_intensity[e_indices[i]-1, ky_indices[i], kx_indices[i]-1]) / 4.0\n", "        )\n", "        \n", "        hessian[1, 2] = hessian[2, 1] = (\n", "            (smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]+1] - \n", "             smoothed_intensity[e_indices[i], ky_indices[i]+1, kx_indices[i]-1] - \n", "             smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]+1] + \n", "             smoothed_intensity[e_indices[i], ky_indices[i]-1, kx_indices[i]-1]) / 4.0\n", "        )\n", "        \n", "        # Calculate eigenvalues to determine the type of critical point\n", "        eigenvalues = np.linalg.e<PERSON><PERSON><PERSON>(hessian)\n", "        \n", "        # Get physical coordinates\n", "        e_value = energy_values[e_indices[i]]\n", "        ky_value = ky_grid[ky_indices[i]]\n", "        kx_value = kx_grid[kx_indices[i]]\n", "        intensity_value = smoothed_intensity[e_indices[i], ky_indices[i], kx_indices[i]]\n", "        \n", "        # Apply offsets to the coordinates for reporting\n", "        e_value_offset = e_value + energy_offset\n", "        ky_value_offset = ky_value + ky_offset\n", "        kx_value_offset = kx_value + kx_offset\n", "        \n", "        # Classify based on eigenvalues\n", "        if np.all(eigenvalues > 0):\n", "            minima.append((e_value_offset, ky_value_offset, kx_value_offset, intensity_value))\n", "        elif np.all(eigenvalues < 0):\n", "            maxima.append((e_value_offset, ky_value_offset, kx_value_offset, intensity_value))\n", "        else:\n", "            saddle_points.append((e_value_offset, ky_value_offset, kx_value_offset, intensity_value))\n", "    \n", "    # Calculate the result\n", "    critical_sum = len(minima) + len(maxima) - len(saddle_points)\n", "    \n", "    print(f\"\\nCritical Point Analysis Results:\")\n", "    print(f\"Minima: {len(minima)}\")\n", "    print(f\"Maxima: {len(maxima)}\")\n", "    print(f\"Saddle points: {len(saddle_points)}\")\n", "    print(f\"Sum (minima + maxima - saddle points): {critical_sum}\")\n", "    \n", "    results = {\n", "        'minima': minima,\n", "        'maxima': maxima,\n", "        'saddle_points': saddle_points,\n", "        'critical_sum': critical_sum\n", "    }\n", "    \n", "    print(f\"Total processing time: {time.time() - start_time:.2f}s\")\n", "    return results\n", "\n", "def create_energy_surface(filename='processed_data.npy', energy_step=None, \n", "                          grid_size=100, intensity_threshold=0.01,\n", "                          colorscale='Viridis', smoothing_sigma=1.0,\n", "                          surface_intensity_threshold=0.1, single_color_mode=False,\n", "                          surface_color='rgb(70,130,180)',\n", "                          kx_range=None, ky_range=None, energy_range=None,\n", "                          kx_offset=0.0, ky_offset=0.0, energy_offset=0.0,\n", "                          show_yellow_plane=False, yellow_plane_energy=None,\n", "                          show_red_plane=False, red_plane_energy=None):\n", "    \"\"\"\n", "    Create a 3D surface visualization from processed ARPES data.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the processed_data.npy file\n", "    energy_step : float or None\n", "        Step size for binding energy slices. If None, use unique values\n", "    grid_size : int\n", "        Size of the interpolation grid (grid_size × grid_size)\n", "    intensity_threshold : float\n", "        Minimum intensity value to consider (for filtering noise)\n", "    colorscale : str\n", "        Plotly colorscale to use for the surface\n", "    smoothing_sigma : float\n", "        Sigma parameter for Gaussian smoothing (higher = more smoothing)\n", "    surface_intensity_threshold : float\n", "        Threshold for including points in the surface visualization\n", "    single_color_mode : bool\n", "        If True, use a single color for the surface instead of intensity coloring\n", "    surface_color : str\n", "        RGB or named color to use if single_color_mode is True\n", "    kx_range : tuple or None\n", "        (min_kx, max_kx) range to include. If None, use full range.\n", "    ky_range : tuple or None\n", "        (min_ky, max_ky) range to include. If None, use full range.\n", "    energy_range : tuple or None\n", "        (min_energy, max_energy) range to include. If None, use full range.\n", "    kx_offset : float\n", "        Offset to apply to kx values for display\n", "    ky_offset : float\n", "        Offset to apply to ky values for display\n", "    energy_offset : float\n", "        Offset to apply to binding energy values for display\n", "    show_yellow_plane : bool\n", "        If True, show a transparent yellow plane at yellow_plane_energy\n", "    yellow_plane_energy : float or None\n", "        Binding energy value for the yellow plane\n", "    show_red_plane : bool\n", "        If True, show a transparent red plane at red_plane_energy\n", "    red_plane_energy : float or None\n", "        Binding energy value for the red plane\n", "    \n", "    Returns:\n", "    --------\n", "    fig : plotly.graph_objects.Figure\n", "        The 3D surface visualization\n", "    \"\"\"\n", "    print(\"Loading data...\")\n", "    start_time = time.time()\n", "    # Load data\n", "    data = np.load(filename)\n", "    df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])\n", "    \n", "    # Filter by intensity to remove noise and reduce computational load\n", "    df = df[df['intensity'] > intensity_threshold]\n", "    \n", "    # Apply range filters if specified\n", "    if kx_range is not None:\n", "        print(f\"Filtering kx range: {kx_range}\")\n", "        df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]\n", "    \n", "    if ky_range is not None:\n", "        print(f\"Filtering ky range: {ky_range}\")\n", "        df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]\n", "    \n", "    if energy_range is not None:\n", "        print(f\"Filtering energy range: {energy_range}\")\n", "        df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]\n", "    \n", "    print(f\"Data loaded with {len(df)} points. Time: {time.time() - start_time:.2f}s\")\n", "    \n", "    # Check if we have enough data after filtering\n", "    if len(df) < 10:\n", "        print(\"Warning: Very few data points remain after filtering. Visualization may be poor.\")\n", "    \n", "    # Define energy values strictly within the specified range\n", "    if energy_step is None:\n", "        # For None step size, use exact unique values\n", "        energy_values = sorted(df['binding_energy'].unique())\n", "    else:\n", "        # For specified step size, create evenly spaced values\n", "        if energy_range is not None:\n", "            min_energy, max_energy = energy_range\n", "        else:\n", "            min_energy = df['binding_energy'].min()\n", "            max_energy = df['binding_energy'].max()\n", "            \n", "        # Create energy values strictly within range\n", "        # Use exclusive max value to ensure we don't exceed the range\n", "        energy_values = np.arange(min_energy, max_energy, energy_step)\n", "        \n", "        # Handle edge case: ensure max_energy is included if very close to range boundary\n", "        if max_energy - energy_values[-1] > 0.01 * energy_step:\n", "            energy_values = np.append(energy_values, max_energy)\n", "    \n", "    # Create a common grid for all slices\n", "    if kx_range is not None:\n", "        kx_min, kx_max = kx_range\n", "    else:\n", "        kx_min, kx_max = df['kx'].min(), df['kx'].max()\n", "    \n", "    if ky_range is not None:\n", "        ky_min, ky_max = ky_range\n", "    else:\n", "        ky_min, ky_max = df['ky'].min(), df['ky'].max()\n", "    \n", "    kx_grid = np.linspace(kx_min, kx_max, grid_size)\n", "    ky_grid = np.linspace(ky_min, ky_max, grid_size)\n", "    KX, KY = np.meshgrid(kx_grid, ky_grid)\n", "    \n", "    # Initialize 3D arrays for surface\n", "    Z = np.zeros((len(energy_values), grid_size, grid_size))\n", "    intensity_grid = np.zeros((len(energy_values), grid_size, grid_size))\n", "    \n", "    print(f\"Processing {len(energy_values)} energy slices in range [{energy_values[0]:.3f}, {energy_values[-1]:.3f}]\")\n", "    \n", "    # Process each energy slice\n", "    for i, energy in enumerate(energy_values):\n", "        if i % 10 == 0:\n", "            print(f\"Processing slice {i}/{len(energy_values)}, energy={energy:.3f}\")\n", "        \n", "        # Filter data for current energy (or energy bin)\n", "        if energy_step is None:\n", "            slice_data = df[df['binding_energy'] == energy]\n", "        else:\n", "            # For last slice, include everything up to max_energy\n", "            if i == len(energy_values) - 1:\n", "                slice_data = df[(df['binding_energy'] >= energy) & \n", "                              (df['binding_energy'] <= max_energy)]\n", "            else:\n", "                slice_data = df[(df['binding_energy'] >= energy) & \n", "                              (df['binding_energy'] < energy_values[i+1])]\n", "        \n", "        if len(slice_data) > 3:  # Need at least 3 points for interpolation\n", "            # Interpolate intensity values to the grid\n", "            intensity_values = griddata(\n", "                (slice_data['kx'], slice_data['ky']), \n", "                slice_data['intensity'],\n", "                (KX, KY),\n", "                method='linear',\n", "                fill_value=0\n", "            )\n", "            \n", "            # Store the interpolated values\n", "            Z[i] = np.full((grid_size, grid_size), energy)\n", "            intensity_grid[i] = intensity_values\n", "    \n", "    # Apply Gaussian smoothing to intensity grid\n", "    print(\"Applying smoothing to the data...\")\n", "    smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)\n", "    \n", "    print(f\"Processing complete. Time: {time.time() - start_time:.2f}s\")\n", "    \n", "    # Create 3D meshgrids\n", "    X_3d = np.repeat(KX[np.newaxis, :, :], len(energy_values), axis=0)\n", "    Y_3d = np.repeat(KY[np.newaxis, :, :], len(energy_values), axis=0)\n", "    \n", "    # Apply offsets to the coordinate grids for visualization\n", "    X_3d_display = X_3d + kx_offset\n", "    Y_3d_display = Y_3d + ky_offset\n", "    Z_display = Z + energy_offset\n", "    \n", "    # Create figure\n", "    fig = go.Figure()\n", "    \n", "    # Apply the surface intensity threshold\n", "    print(f\"Applying surface intensity threshold: {surface_intensity_threshold}\")\n", "    intensity_values = smoothed_intensity.flatten()\n", "    \n", "    # Only include points that are above the threshold\n", "    mask = intensity_values >= surface_intensity_threshold\n", "    \n", "    print(f\"Filtered surface contains {np.sum(mask)} points out of {len(mask)}\")\n", "    \n", "    # Create either a colored surface or single-color surface based on the toggle\n", "    if single_color_mode:\n", "        print(\"Creating single-color surface visualization...\")\n", "        # For single color mode, we create a binary mask for the isosurface\n", "        binary_values = np.zeros_like(smoothed_intensity.flatten())\n", "        binary_values[mask] = 1.0\n", "        \n", "        fig.add_trace(go.<PERSON><PERSON>urface(\n", "            x=X_3d_display.flatten(),  # Use offset-adjusted coordinates\n", "            y=Y_3d_display.flatten(),  # Use offset-adjusted coordinates\n", "            z=Z_display.flatten(),     # Use offset-adjusted coordinates\n", "            value=binary_values,\n", "            isomin=0.5,  # Binary threshold\n", "            isomax=1.0,\n", "            surface_count=1,  # Only one surface for binary data\n", "            opacity=0.9,\n", "            colorscale=[[0, 'rgba(0,0,0,0)'], [1, surface_color]],  # Single color for surface\n", "            caps=dict(x_show=False, y_show=False, z_show=False),\n", "            showscale=False  # Hide colorbar\n", "        ))\n", "    else:\n", "        print(\"Creating intensity-colored surface visualization...\")\n", "        # If we want a colored surface based on intensity\n", "        fig.add_trace(go.<PERSON><PERSON>urface(\n", "            x=X_3d_display.flatten(),  # Use offset-adjusted coordinates\n", "            y=Y_3d_display.flatten(),  # Use offset-adjusted coordinates\n", "            z=Z_display.flatten(),     # Use offset-adjusted coordinates\n", "            value=intensity_values,\n", "            isomin=surface_intensity_threshold,\n", "            isomax=np.max(intensity_values) * 0.9, # Top 10% for high intensity regions\n", "            surface_count=20,\n", "            opacity=0.99999999,\n", "            colorscale=colorscale,\n", "            caps=dict(x_show=False, y_show=False, z_show=False)\n", "        ))\n", "    \n", "    # Add yellow energy plane if enabled\n", "    if show_yellow_plane and yellow_plane_energy is not None:\n", "        # Verify energy is within range\n", "        if (energy_range is None or \n", "            (energy_range[0] <= yellow_plane_energy <= energy_range[1])):\n", "            print(f\"Adding yellow plane at energy = {yellow_plane_energy}\")\n", "            # Create a mesh for the plane with offset applied\n", "            plane_z = np.full((2, 2), yellow_plane_energy + energy_offset)  # Apply energy offset\n", "            plane_x = np.array([[kx_min + kx_offset, kx_max + kx_offset],  # Apply kx offset\n", "                                [kx_min + kx_offset, kx_max + kx_offset]])\n", "            plane_y = np.array([[ky_min + ky_offset, ky_min + ky_offset],  # Apply ky offset\n", "                                [ky_max + ky_offset, ky_max + ky_offset]])\n", "            \n", "            fig.add_trace(go.Surface(\n", "                z=plane_z,\n", "                x=plane_x,\n", "                y=plane_y,\n", "                colorscale=[[0, 'rgba(255,255,0,0.3)'], [1, 'rgba(255,255,0,0.3)']],\n", "                showscale=False,\n", "                name=f\"E = {yellow_plane_energy + energy_offset:.3f}\",  # Update label with offset\n", "                hoverinfo=\"name\",\n", "                opacity=0.5\n", "            ))\n", "    \n", "    # Add red energy plane if enabled\n", "    if show_red_plane and red_plane_energy is not None:\n", "        # Verify energy is within range\n", "        if (energy_range is None or \n", "            (energy_range[0] <= red_plane_energy <= energy_range[1])):\n", "            print(f\"Adding red plane at energy = {red_plane_energy}\")\n", "            # Create a mesh for the plane with offset applied\n", "            plane_z = np.full((2, 2), red_plane_energy + energy_offset)  # Apply energy offset\n", "            plane_x = np.array([[kx_min + kx_offset, kx_max + kx_offset],  # Apply kx offset\n", "                                [kx_min + kx_offset, kx_max + kx_offset]])\n", "            plane_y = np.array([[ky_min + ky_offset, ky_min + ky_offset],  # Apply ky offset\n", "                                [ky_max + ky_offset, ky_max + ky_offset]])\n", "            \n", "            fig.add_trace(go.Surface(\n", "                z=plane_z,\n", "                x=plane_x,\n", "                y=plane_y,\n", "                colorscale=[[0, 'rgba(255,0,0,0.3)'], [1, 'rgba(255,0,0,0.3)']],\n", "                showscale=False,\n", "                name=f\"E = {red_plane_energy + energy_offset:.3f}\",  # Update label with offset\n", "                hoverinfo=\"name\",\n", "                opacity=0.5\n", "            ))\n", "    \n", "    # Determine axis range labels for the title\n", "    kx_label = f\"kx: [{kx_min:.2f} to {kx_max:.2f}]\"\n", "    ky_label = f\"ky: [{ky_min:.2f} to {ky_max:.2f}]\"\n", "    energy_label = f\"E: [{energy_values[0]:.2f} to {energy_values[-1]:.2f}]\"\n", "    range_title = f\"{kx_label}, {ky_label}, {energy_label}\"\n", "    \n", "    # Update layout with range information\n", "    fig.update_layout(\n", "        title=f\"ARPES Data 3D Surface<br><sub>{range_title}</sub>\",\n", "        scene=dict(\n", "            xaxis_title=r\"k<sub>x</sub> (1/Å)\",\n", "            yaxis_title=r\"k<sub>y</sub> (1/Å)\",\n", "            zaxis_title=r\"E<sub>b</sub> (eV)\",\n", "            aspectratio=dict(x=1, y=1, z=1),  # Equal scale for all axes\n", "            # Explicitly set axis ranges to match our constraints with offsets\n", "            xaxis_range=[kx_min + kx_offset, kx_max + kx_offset],\n", "            yaxis_range=[ky_min + ky_offset, ky_max + ky_offset],\n", "            zaxis_range=[energy_values[0] + energy_offset, energy_values[-1] + energy_offset],\n", "            xaxis=dict(tickfont=dict(size=12), title_font=dict(size=20)),\n", "            yaxis=dict(tickfont=dict(size=12), title_font=dict(size=20)),\n", "            zaxis=dict(tickfont=dict(size=12), title_font=dict(size=20))\n", "        ),\n", "        margin=dict(l=0, r=0, b=0, t=60)  # Increased top margin for the subtitle\n", "    )\n", "    \n", "    print(f\"Total processing time: {time.time() - start_time:.2f}s\")\n", "    return fig\n", "\n", "# Example usage with energy range constraint:\n", "fig = create_energy_surface(\n", "    energy_step=0.01,\n", "    grid_size=200,\n", "    smoothing_sigma=1.3,\n", "    surface_intensity_threshold=0.2,\n", "    energy_range=(-1.1, 0.1),\n", "    ky_range=(-0.9, 0.1),\n", "    kx_range=(-0.5, 0.5),\n", "    kx_offset=0.4,  # Example offset for kx\n", "    ky_offset=0.0,  # Example offset for ky\n", "    energy_offset=0.0,  # Example offset for binding energy\n", "    show_yellow_plane=True,\n", "    yellow_plane_energy=0,\n", "    show_red_plane=True,\n", "    red_plane_energy=-1.0,\n", "    single_color_mode=True\n", ")\n", "fig.show(renderer=\"browser\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9500f01a", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import plotly.graph_objects as go\n", "from scipy.interpolate import griddata\n", "from scipy.ndimage import gaussian_filter\n", "import time\n", "def create_integrated_projections_from_surface(filename='processed_data.npy', \n", "                                 grid_size=200, \n", "                                 intensity_threshold=0.01,\n", "                                 smoothing_sigma=1.0,\n", "                                 binary_threshold=0.15,\n", "                                 single_color_mode=False,\n", "                                 kx_range=None, \n", "                                 ky_range=None, \n", "                                 energy_range=None,\n", "                                 kx_offset=0.0,\n", "                                 ky_offset=0.0,\n", "                                 energy_offset=0.0,\n", "                                 colorscale='Viridis',\n", "                                 show_all=True):\n", "    \"\"\"\n", "    Create integrated projections from processed 3D ARPES surface data.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the processed_data.npy file\n", "    grid_size : int\n", "        Size of the interpolation grid\n", "    intensity_threshold : float\n", "        Minimum intensity value to consider (for filtering noise)\n", "    smoothing_sigma : float\n", "        Sigma parameter for Gaussian smoothing\n", "    binary_threshold : float\n", "        Threshold for binarizing intensity in single_color_mode\n", "    single_color_mode : bool\n", "        If True, binarize intensities before integration\n", "    kx_range : tuple or None\n", "        (min_kx, max_kx) range to include\n", "    ky_range : tuple or None\n", "        (min_ky, max_ky) range to include\n", "    energy_range : tuple or None\n", "        (min_energy, max_energy) range to include\n", "    kx_offset : float\n", "        Offset to apply to kx values for display\n", "    ky_offset : float\n", "        Offset to apply to ky values for display\n", "    energy_offset : float\n", "        Offset to apply to binding energy values for display\n", "    colorscale : str\n", "        Plotly colorscale to use\n", "    show_all : bool\n", "        If True, display all three projections; if False, return the figures\n", "        \n", "    Returns:\n", "    --------\n", "    dict or None\n", "        Dictionary containing the three projection figures if show_all=False\n", "    \"\"\"\n", "    print(\"Loading and processing data...\")\n", "    start_time = time.time()\n", "    \n", "    # Load data\n", "    data = np.load(filename)\n", "    df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])\n", "    \n", "    # Filter by intensity to remove noise\n", "    df = df[df['intensity'] > intensity_threshold]\n", "    \n", "    # Apply range filters if specified\n", "    if kx_range is not None:\n", "        df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]\n", "    \n", "    if ky_range is not None:\n", "        df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]\n", "    \n", "    if energy_range is not None:\n", "        df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]\n", "    \n", "    # Define grid limits\n", "    kx_min = kx_range[0] if kx_range is not None else df['kx'].min()\n", "    kx_max = kx_range[1] if kx_range is not None else df['kx'].max()\n", "    \n", "    ky_min = ky_range[0] if ky_range is not None else df['ky'].min()\n", "    ky_max = ky_range[1] if ky_range is not None else df['ky'].max()\n", "    \n", "    energy_min = energy_range[0] if energy_range is not None else df['binding_energy'].min()\n", "    energy_max = energy_range[1] if energy_range is not None else df['binding_energy'].max()\n", "    \n", "    # Create grid arrays\n", "    kx_grid = np.linspace(kx_min, kx_max, grid_size)\n", "    ky_grid = np.linspace(ky_min, ky_max, grid_size)\n", "    energy_grid = np.linspace(energy_max, energy_min, grid_size)\n", "    \n", "    # Create 3D meshgrids\n", "    KX, KY, E = np.meshgrid(kx_grid, ky_grid, energy_grid, indexing='ij')\n", "    \n", "    # Build 3D intensity array - this is the key step for continuous surface\n", "    print(\"Building 3D intensity grid...\")\n", "    intensity_grid = np.zeros((grid_size, grid_size, grid_size))\n", "    \n", "    # For each energy slice, interpolate intensity\n", "    for k, energy in enumerate(energy_grid):\n", "        if k % 20 == 0:\n", "            print(f\"Processing energy slice {k}/{grid_size}, energy={energy:.3f}\")\n", "        \n", "        # Filter data for current energy bin\n", "        energy_step = (energy_max - energy_min) / grid_size\n", "        slice_data = df[(df['binding_energy'] >= energy - energy_step/2) & \n", "                      (df['binding_energy'] < energy + energy_step/2)]\n", "        \n", "        if len(slice_data) > 3:  # Need at least 3 points for interpolation\n", "            # Interpolate intensity values to the grid\n", "            points = np.column_stack((slice_data['kx'], slice_data['ky']))\n", "            values = slice_data['intensity']\n", "            \n", "            # Create a 2D grid for this energy slice\n", "            grid_x, grid_y = np.meshgrid(kx_grid, ky_grid, indexing='ij')\n", "            grid_points = np.column_stack((grid_x.flatten(), grid_y.flatten()))\n", "            \n", "            # Interpolate\n", "            grid_values = griddata(points, values, grid_points, method='linear', fill_value=0)\n", "            intensity_grid[:, :, k] = grid_values.reshape(grid_size, grid_size)\n", "    \n", "    # Apply Gaussian smoothing to intensity grid for continuity\n", "    print(\"Applying 3D smoothing...\")\n", "    smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)\n", "    \n", "    # Apply binary threshold if in single color mode\n", "    if single_color_mode:\n", "        print(f\"Applying binary threshold {binary_threshold}...\")\n", "        binary_intensity = (smoothed_intensity >= binary_threshold).astype(float)\n", "        processed_intensity = binary_intensity\n", "    else:\n", "        processed_intensity = smoothed_intensity\n", "    \n", "    # Create projections by integrating along each axis\n", "    print(\"Creating projections by integration...\")\n", "    \n", "    # 1. E vs kx projection (integrating over ky)\n", "    e_kx_projection = np.sum(processed_intensity, axis=1).T  # Sum along ky axis\n", "    \n", "    # 2. E vs ky projection (integrating over kx)\n", "    e_ky_projection = np.sum(processed_intensity, axis=0).T  # Sum along kx axis\n", "    \n", "    # 3. kx vs ky projection (integrating over energy)\n", "    kx_ky_projection = np.sum(processed_intensity, axis=2)   # Sum along energy axis\n", "    \n", "    # Apply offsets to the grid values for display\n", "    kx_grid_display = kx_grid + kx_offset\n", "    ky_grid_display = ky_grid + ky_offset\n", "    energy_grid_display = energy_grid + energy_offset\n", "    \n", "    # Create figures\n", "    print(\"Creating visualization plots...\")\n", "    # 1. E vs kx projection\n", "    fig_e_kx = go.Figure(data=go.Heatmap(\n", "        z=e_kx_projection,\n", "        x=kx_grid_display,  # Apply kx offset\n", "        y=energy_grid_display,  # Apply energy offset\n", "        colorscale=colorscale,\n", "        zmin=0,\n", "        zmax=None,  # Auto-scale maximum\n", "    ))\n", "    \n", "    fig_e_kx.update_layout(\n", "        title=r\"E<sub>b</sub> vs k<sub>x</sub> Projection (Integrated over k<sub>y</sub>)\",\n", "        xaxis_title=r\"k<sub>x</sub> (1/Å)\",\n", "        yaxis_title=r\"E<sub>b</sub> (eV)\",\n", "        width=700,\n", "        height=600,\n", "        xaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16)),\n", "        yaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16))\n", "    )\n", "    \n", "    # 2. E vs ky projection\n", "    fig_e_ky = go.Figure(data=go.Heatmap(\n", "        z=e_ky_projection,\n", "        x=ky_grid_display,  # Apply ky offset\n", "        y=energy_grid_display,  # Apply energy offset\n", "        colorscale=colorscale,\n", "        zmin=0,\n", "        zmax=None,  # Auto-scale maximum\n", "    ))\n", "    \n", "    fig_e_ky.update_layout(\n", "        title=r\"E<sub>b</sub> vs k<sub>y</sub> Projection (Integrated over k<sub>x</sub>)\",\n", "        xaxis_title=r\"k<sub>y</sub> (1/Å)\",\n", "        yaxis_title=r\"E<sub>b</sub> (eV)\",\n", "        width=700,\n", "        height=600,\n", "        xaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16)),\n", "        yaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16))\n", "    )\n", "    \n", "    # 3. kx vs ky projection\n", "    fig_kx_ky = go.Figure(data=go.Heatmap(\n", "        z=kx_ky_projection,\n", "        x=kx_grid_display,  # Apply kx offset\n", "        y=ky_grid_display,  # Apply ky offset\n", "        colorscale=colorscale,\n", "        zmin=0,\n", "        zmax=None,  # Auto-scale maximum\n", "    ))\n", "    \n", "    fig_kx_ky.update_layout(\n", "        title=r\"k<sub>x</sub> vs k<sub>y</sub> Projection (Integrated over Binding Energy)\",\n", "        xaxis_title=r\"k<sub>x</sub> (1/Å)\",\n", "        yaxis_title=r\"k<sub>y</sub> (1/Å)\",\n", "        width=700,\n", "        height=600,\n", "        xaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16)),\n", "        yaxis=dict(tickfont=dict(size=14), titlefont=dict(size=16))\n", "    )\n", "    \n", "    print(f\"Total processing time: {time.time() - start_time:.2f}s\")\n", "    \n", "    if show_all:\n", "        fig_e_kx.show()\n", "        fig_e_ky.show()\n", "        fig_kx_ky.show()\n", "        return None\n", "    else:\n", "        return {\n", "            'e_kx': fig_e_kx,\n", "            'e_ky': fig_e_ky,\n", "            'kx_ky': fig_kx_ky\n", "        }\n", "\n", "# Example usage:\n", "projections = create_integrated_projections_from_surface(\n", "    grid_size=100,\n", "    smoothing_sigma=1,\n", "    binary_threshold=0.15,\n", "    single_color_mode=True,  # Set to True for binary mode\n", "    energy_range=(-1.1, -0.01),\n", "    ky_range=(-0.9, 0.1),\n", "    kx_range=(-0.5, 0.5),\n", "    kx_offset=0.0,  # Example offset for kx\n", "    ky_offset=0.4,  # Example offset for ky\n", "    energy_offset=0.0,  # Example offset for binding energy\n", "    colorscale='bluered',\n", "    show_all=True\n", ")\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}