igor_data = np.array([
    [57600, 54784, 58112],
    [56561.95, 53415.66, 57121.13],
    [55523.89, 52047.31, 56130.26],
    [54485.84, 50678.96, 55139.39],
    [53447.78, 49310.62, 54148.52],
    [52409.73, 47942.27, 53157.65],
    [51371.67, 46655.25, 52193.88],
    [50333.62, 45428.45, 51250.2],
    [49295.56, 44201.66, 50306.51],
    [48257.51, 42974.87, 49362.82],
    [47219.45, 41748.08, 48419.14],
    [46223.56, 40563.45, 47510.59],
    [45468.61, 39619.77, 46802.82],
    [44713.66, 38676.08, 46095.06],
    [43958.71, 37732.39, 45387.29],
    [43203.77, 36788.71, 44679.53],
    [42448.82, 35845.02, 43971.77],
    [41693.87, 34833.07, 43195.73],
    [40938.92, 33795.01, 42393.6],
    [40183.97, 32756.96, 41591.46],
    [39429.02, 31718.9, 40789.33],
    [38674.07, 30680.85, 39987.2],
    [37919.12, 29670.9, 39171.01],
    [37164.17, 28727.21, 38321.7],
    [36409.22, 27783.53, 37472.38],
    [35654.27, 26839.84, 36623.06],
    [34899.32, 25896.16, 35773.74],
    [34144.38, 24952.47, 34924.42],
    [33512.91, 24173.43, 34239.75],
    [32899.52, 23418.48, 33579.17],
    [32286.12, 22663.53, 32918.59],
    [31672.72, 21908.58, 32258.01],
    [31059.33, 21153.63, 31597.43],
    [30467.01, 20419.77, 30957.93],
    [29900.8, 19712, 30344.53],
    [29334.59, 19004.23, 29731.14],
    [28768.38, 18296.47, 29117.74],
    [28202.16, 17588.71, 28504.35],
    [27641.98, 16886.96, 27901.99],
    [27358.87, 16462.31, 27807.62],
    [27075.77, 16037.65, 27713.26],
    [26792.66, 15612.99, 27618.89],
    [26509.55, 15188.33, 27524.52],
    [26226.45, 14763.67, 27430.15],
    [26027.67, 14479.56, 27448.22],
    [25886.12, 14290.82, 27542.59],
    [25744.56, 14102.09, 27636.96],
    [25603.01, 13913.35, 27731.33],
    [25461.46, 13724.61, 27825.69],
    [25279.75, 13503.75, 27944.16],
    [24902.28, 13126.27, 28180.08],
    [24524.8, 12748.8, 28416],
    [24147.33, 12371.33, 28651.92],
    [23769.85, 11993.85, 28887.84],
    [23392.38, 11616.38, 29123.77],
    [22874.35, 11168.63, 29359.69],
    [22308.14, 10696.78, 29595.61],
    [21741.93, 10224.94, 29831.53],
    [21175.72, 9753.098, 30067.45],
    [20609.51, 9281.255, 30303.37],
    [19952.94, 8899.765, 30539.29],
    [19103.62, 8711.027, 30775.21],
    [18254.31, 8522.29, 31011.14],
    [17404.99, 8333.553, 31247.06],
    [16555.67, 8144.816, 31482.98],
    [15706.35, 7956.079, 31718.9],
    [14688.38, 7893.835, 31828.33],
    [13650.32, 7846.651, 31922.7],
    [12612.27, 7799.467, 32017.07],
    [11574.21, 7752.282, 32111.44],
    [10536.16, 7705.098, 32205.8],
    [9807.31, 7922.949, 32388.52],
    [9429.835, 8441.977, 32671.62],
    [9052.36, 8961.004, 32954.73],
    [8674.887, 9480.031, 33237.84],
    [8297.412, 9999.059, 33520.94],
    [7911.906, 10526.12, 33812.08],
    [7345.694, 11233.88, 34283.92],
    [6779.482, 11941.65, 34755.77],
    [6213.271, 12649.41, 35227.61],
    [5647.059, 13357.18, 35699.45],
    [5080.847, 14064.94, 36171.29],
    [4543.749, 14714.48, 36614.02],
    [4024.722, 15327.87, 37038.68],
    [3505.694, 15941.27, 37463.34],
    [2986.667, 16554.67, 37888],
    [2467.639, 17168.06, 38312.66],
    [1984.753, 17790.49, 38764.42],
    [1654.463, 18451.07, 39330.64],
    [1324.173, 19111.65, 39896.85],
    [993.8823, 19772.23, 40463.06],
    [663.5922, 20432.82, 41029.27],
    [333.302, 21093.4, 41595.48],
    [256, 21464.85, 41944.85],
    [256, 21747.95, 42227.95],
    [256, 22031.06, 42511.06],
    [256, 22314.16, 42794.16],
    [256, 22597.27, 43077.27],
    [239.9373, 23008.88, 43456.75],
    [192.7529, 23669.46, 44022.96],
    [145.5686, 24330.04, 44589.18],
    [98.38432, 24990.62, 45155.39],
    [51.2, 25651.2, 45721.6],
    [4.015687, 26311.78, 46287.81],
    [0, 26972.36, 46897.19],
    [0, 27632.94, 47510.59],
    [0, 28293.52, 48123.98],
    [0, 28954.1, 48737.38],
    [0, 29614.68, 49350.78],
    [0, 30344.53, 50033.44],
    [0, 31146.67, 50788.39],
    [0, 31948.8, 51543.34],
    [0, 32750.93, 52298.29],
    [0, 33553.07, 53053.24],
    [0, 34358.21, 53805.18],
    [0, 35207.53, 54512.94],
    [0, 36056.85, 55220.71],
    [0, 36906.16, 55928.47],
    [0, 37755.48, 56636.23],
    [0, 38604.8, 57344],
    [0, 39062.59, 57208.47],
    [0, 39298.51, 56595.07],
    [0, 39534.43, 55981.68],
    [0, 39770.35, 55368.28],
    [0, 40006.27, 54754.89],
    [0, 40181.96, 54041.1],
    [0, 40134.78, 52955.86],
    [0, 40087.59, 51870.62],
    [0, 40040.41, 50785.38],
    [0, 39993.22, 49700.14],
    [0, 39946.04, 48614.9],
    [0, 39936, 47641.1],
    [0, 39936, 46697.41],
    [0, 39936, 45753.73],
    [0, 39936, 44810.04],
    [0, 39936, 43866.35],
    [0, 39918.93, 42854.4],
    [0, 39871.75, 41721.98],
    [0, 39824.57, 40589.55],
    [0, 39777.38, 39457.13],
    [0, 39730.2, 38324.71],
    [0, 39683.01, 37192.28],
    [0, 39680, 36369.07],
    [0, 39680, 35566.93],
    [0, 39680, 34764.8],
    [0, 39680, 33962.67],
    [0, 39680, 33160.54],
    [0, 39680, 32527.06],
    [0, 39680, 32055.21],
    [0, 39680, 31583.37],
    [0, 39680, 31111.53],
    [0, 39680, 30639.69],
    [0, 39675.98, 30123.67],
    [0, 39628.8, 29132.8],
    [0, 39581.62, 28141.93],
    [0, 39534.43, 27151.06],
    [0, 39487.25, 26160.19],
    [0, 39440.06, 25169.32],
    [0, 39361.76, 24240.69],
    [0, 39267.39, 23344.19],
    [0, 39173.02, 22447.69],
    [0, 39078.65, 21551.18],
    [0, 38984.28, 20654.68],
    [0, 38923.04, 19835.48],
    [0, 38970.23, 19269.27],
    [0, 39017.41, 18703.06],
    [0, 39064.6, 18136.85],
    [0, 39111.78, 17570.63],
    [0, 39158.96, 17004.42],
    [0, 39435.04, 16781.55],
    [0, 39765.33, 16640],
    [0, 40095.62, 16498.45],
    [0, 40425.91, 16356.89],
    [0, 40756.2, 16215.34],
    [993.8823, 41122.64, 16073.79],
    [3589.02, 41547.29, 15932.24],
    [6184.157, 41971.95, 15790.68],
    [8779.294, 42396.61, 15649.13],
    [11374.43, 42821.27, 15507.58],
    [13969.57, 43245.93, 15366.02],
    [15796.71, 43715.77, 15224.47],
    [17589.71, 44187.61, 15082.92],
    [19382.71, 44659.45, 14941.36],
    [21175.72, 45131.29, 14799.81],
    [22968.72, 45603.14, 14658.26],
    [24686.43, 46100.08, 14516.71],
    [26337.88, 46619.11, 14375.15],
    [27989.33, 47138.13, 14233.6],
    [29640.79, 47657.16, 14092.05],
    [31292.23, 48176.19, 13950.49],
    [32933.65, 48705.25, 13798.9],
    [34490.73, 49318.65, 13562.98],
    [36047.81, 49932.05, 13327.06],
    [37604.89, 50545.44, 13091.14],
    [39161.98, 51158.84, 12855.22],
    [40719.06, 51772.23, 12619.29],
    [41922.76, 52225, 12415.5],
    [42960.82, 52602.48, 12226.76],
    [43998.87, 52979.95, 12038.02],
    [45036.93, 53357.43, 11849.29],
    [46074.98, 53734.9, 11660.55],
    [47293.74, 54196.71, 11411.58],
    [49039.56, 54904.47, 10986.92],
    [50785.38, 55612.23, 10562.26],
    [52531.2, 56320, 10137.6],
    [54277.02, 57027.77, 9712.941],
    [56022.84, 57735.53, 9288.282],
    [57494.59, 58325.84, 8785.317],
    [58910.12, 58892.05, 8266.29],
    [60325.65, 59458.26, 7747.263],
    [61741.18, 60024.47, 7228.235],
    [63156.71, 60590.68, 6709.208],
    [64076.3, 60470.21, 6457.224],
    [64265.04, 59337.79, 6598.776],
    [64453.77, 58205.36, 6740.33],
    [64642.51, 57072.94, 6881.882],
    [64831.25, 55940.52, 7023.435],
    [65019.98, 54808.09, 7164.988],
    [64746.92, 53260.05, 7398.902],
    [64463.81, 51702.96, 7634.824],
    [64180.71, 50145.88, 7870.745],
    [63897.6, 48588.8, 8106.667],
    [63614.49, 47031.72, 8342.588],
    [63592.41, 45605.14, 8474.102],
    [63781.14, 44283.98, 8521.286],
    [63969.88, 42962.82, 8568.471],
    [64158.62, 41641.66, 8615.655],
    [64347.36, 40320.5, 8662.839],
    [64415.62, 38993.32, 8704],
    [63660.68, 37624.97, 8704],
    [62905.73, 36256.63, 8704],
    [62150.78, 34888.28, 8704],
    [61395.83, 33519.94, 8704],
    [60640.88, 32151.59, 8704],
    [60283.48, 30882.63, 8704],
    [60094.75, 29655.84, 8704],
    [59906.01, 28429.05, 8704],
    [59717.27, 27202.26, 8704],
    [59528.54, 25975.47, 8704],
    [59339.8, 24722.57, 8704],
    [59151.06, 23401.41, 8704],
    [58962.32, 22080.25, 8704],
    [58773.59, 20759.09, 8704],
    [58584.85, 19437.93, 8704],
    [58396.11, 18116.77, 8704],
    [58287.69, 17197.18, 8704],
    [58193.32, 16347.86, 8704],
    [58098.95, 15498.54, 8704],
    [58004.58, 14649.22, 8704],
    [57910.21, 13799.91, 8704],
    [57795.77, 12267.92, 8704],
    [57654.21, 9814.337, 8704],
    [57512.66, 7360.753, 8704],
    [57371.11, 4907.168, 8704],
    [57229.55, 2453.584, 8704],
    [57088, 0, 8704]])

#!/usr/bin/env python3
"""
Enhanced ARPES Analysis GUI - Standalone Window Application
A complete GUI application for ARPES data analysis with advanced Plotly visualization.
Includes all functionality from ThesisAnalysis.ipynb with live plot updates.

Features:
- Interactive Plotly plots with zoom, pan, hover
- Multiple plot modes: E vs kx, kx vs ky, kx vs kz
- Advanced analysis: peak detection, edge detection, Euler characteristic, critical points
- Live plot updates when parameters change
- Canny edge detection with customizable parameters
- Component analysis and labeling
- Custom colorscales including Igor-style rainbow
- Export functionality

Optimized for pyarpesenv anaconda environment.
"""

import os
import sys
import warnings
import time
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter.ttk import Progressbar
import threading
import webbrowser
import tempfile
from pathlib import Path

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Handle numpy complex deprecation
if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Try to import required packages with better error handling
missing_packages = []

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
except ImportError as e:
    missing_packages.append(f"plotly: {e}")

try:
    from scipy.interpolate import griddata
    from scipy.ndimage import gaussian_filter, maximum_filter
    from scipy.spatial import cKDTree
except ImportError as e:
    missing_packages.append(f"scipy: {e}")

try:
    from skimage.feature import canny
    from skimage.measure import label, euler_number
except ImportError as e:
    missing_packages.append(f"scikit-image: {e}")

try:
    from arpes.load_pxt import read_single_pxt
except ImportError as e:
    missing_packages.append(f"arpes: {e}")
    print("⚠️  Warning: ARPES package not found. Make sure you're in the pyarpesenv environment.")
    print("   Activate with: conda activate pyarpesenv")

try:
    import tkinter.html as tkhtml
    HAS_TKHTML = True
except ImportError:
    try:
        import tkinterhtml as tkhtml
        HAS_TKHTML = True
    except ImportError:
        HAS_TKHTML = False

try:
    import webview
    HAS_WEBVIEW = True
except ImportError:
    HAS_WEBVIEW = False

if missing_packages:
    print("❌ Missing required packages:")
    for pkg in missing_packages:
        print(f"   - {pkg}")
    print("\n📋 To fix this in pyarpesenv environment:")
    print("   conda activate pyarpesenv")
    print("   conda install plotly pandas numpy scipy scikit-image")
    print("   # or use pip if conda doesn't have the package:")
    print("   pip install plotly")

    # Don't exit immediately - let user see the error in GUI
    messagebox.showerror("Missing Dependencies",
                        f"Missing packages: {', '.join([p.split(':')[0] for p in missing_packages])}\n\n"
                        "Please install them in pyarpesenv environment:\n"
                        "conda activate pyarpesenv\n"
                        "conda install plotly pandas numpy scipy scikit-image")
    sys.exit(1)

# Constants
WORK_FUNCTION = 4.5  # eV
V0 = 10  # Inner potential in eV

# Custom colormap data (truncated for brevity - you can add the full array)
igor_data = np.array([
    [57600, 54784, 58112],
    [56561.95, 53415.66, 57121.13],
    [55523.89, 52047.31, 56130.26],
    [54485.84, 50678.96, 55139.39],
    [53447.78, 49310.62, 54148.52],
    [52409.73, 47942.27, 53157.65],
    [51371.67, 46655.25, 52193.88],
    [50333.62, 45428.45, 51250.2],
    [49295.56, 44201.66, 50306.51],
    [48257.51, 42974.87, 49362.82],
    [47219.45, 41748.08, 48419.14],
    [46223.56, 40563.45, 47510.59],
    [45468.61, 39619.77, 46802.82],
    [44713.66, 38676.08, 46095.06],
    [43958.71, 37732.39, 45387.29],
    [43203.77, 36788.71, 44679.53],
    [42448.82, 35845.02, 43971.77],
    [41693.87, 34833.07, 43195.73],
    [40938.92, 33795.01, 42393.6],
    [40183.97, 32756.96, 41591.46],
    [39429.02, 31718.9, 40789.33],
    [38674.07, 30680.85, 39987.2],
    [37919.12, 29670.9, 39171.01],
    [37164.17, 28727.21, 38321.7],
    [36409.22, 27783.53, 37472.38],
    [35654.27, 26839.84, 36623.06],
    [34899.32, 25896.16, 35773.74],
    [34144.38, 24952.47, 34924.42],
    [33512.91, 24173.43, 34239.75],
    [32899.52, 23418.48, 33579.17],
    [32286.12, 22663.53, 32918.59],
    [31672.72, 21908.58, 32258.01],
    [31059.33, 21153.63, 31597.43],
    [30467.01, 20419.77, 30957.93],
    [29900.8, 19712, 30344.53],
    [29334.59, 19004.23, 29731.14],
    [28768.38, 18296.47, 29117.74],
    [28202.16, 17588.71, 28504.35],
    [27641.98, 16886.96, 27901.99],
    [27358.87, 16462.31, 27807.62],
    [27075.77, 16037.65, 27713.26],
    [26792.66, 15612.99, 27618.89],
    [26509.55, 15188.33, 27524.52],
    [26226.45, 14763.67, 27430.15],
    [26027.67, 14479.56, 27448.22],
    [25886.12, 14290.82, 27542.59],
    [25744.56, 14102.09, 27636.96],
    [25603.01, 13913.35, 27731.33],
    [25461.46, 13724.61, 27825.69],
    [25279.75, 13503.75, 27944.16],
    [24902.28, 13126.27, 28180.08],
    [24524.8, 12748.8, 28416],
    [24147.33, 12371.33, 28651.92],
    [23769.85, 11993.85, 28887.84],
    [23392.38, 11616.38, 29123.77],
    [22874.35, 11168.63, 29359.69],
    [22308.14, 10696.78, 29595.61],
    [21741.93, 10224.94, 29831.53],
    [21175.72, 9753.098, 30067.45],
    [20609.51, 9281.255, 30303.37],
    [19952.94, 8899.765, 30539.29],
    [19103.62, 8711.027, 30775.21],
    [18254.31, 8522.29, 31011.14],
    [17404.99, 8333.553, 31247.06],
    [16555.67, 8144.816, 31482.98],
    [15706.35, 7956.079, 31718.9],
    [14688.38, 7893.835, 31828.33],
    [13650.32, 7846.651, 31922.7],
    [12612.27, 7799.467, 32017.07],
    [11574.21, 7752.282, 32111.44],
    [10536.16, 7705.098, 32205.8],
    [9807.31, 7922.949, 32388.52],
    [9429.835, 8441.977, 32671.62],
    [9052.36, 8961.004, 32954.73],
    [8674.887, 9480.031, 33237.84],
    [8297.412, 9999.059, 33520.94],
    [7911.906, 10526.12, 33812.08],
    [7345.694, 11233.88, 34283.92],
    [6779.482, 11941.65, 34755.77],
    [6213.271, 12649.41, 35227.61],
    [5647.059, 13357.18, 35699.45],
    [5080.847, 14064.94, 36171.29],
    [4543.749, 14714.48, 36614.02],
    [4024.722, 15327.87, 37038.68],
    [3505.694, 15941.27, 37463.34],
    [2986.667, 16554.67, 37888],
    [2467.639, 17168.06, 38312.66],
    [1984.753, 17790.49, 38764.42],
    [1654.463, 18451.07, 39330.64],
    [1324.173, 19111.65, 39896.85],
    [993.8823, 19772.23, 40463.06],
    [663.5922, 20432.82, 41029.27],
    [333.302, 21093.4, 41595.48],
    [256, 21464.85, 41944.85],
    [256, 21747.95, 42227.95],
    [256, 22031.06, 42511.06],
    [256, 22314.16, 42794.16],
    [256, 22597.27, 43077.27],
    [239.9373, 23008.88, 43456.75],
    [192.7529, 23669.46, 44022.96],
    [145.5686, 24330.04, 44589.18],
    [98.38432, 24990.62, 45155.39],
    [51.2, 25651.2, 45721.6],
    [4.015687, 26311.78, 46287.81],
    [0, 26972.36, 46897.19],
    [0, 27632.94, 47510.59],
    [0, 28293.52, 48123.98],
    [0, 28954.1, 48737.38],
    [0, 29614.68, 49350.78],
    [0, 30344.53, 50033.44],
    [0, 31146.67, 50788.39],
    [0, 31948.8, 51543.34],
    [0, 32750.93, 52298.29],
    [0, 33553.07, 53053.24],
    [0, 34358.21, 53805.18],
    [0, 35207.53, 54512.94],
    [0, 36056.85, 55220.71],
    [0, 36906.16, 55928.47],
    [0, 37755.48, 56636.23],
    [0, 38604.8, 57344],
    [0, 39062.59, 57208.47],
    [0, 39298.51, 56595.07],
    [0, 39534.43, 55981.68],
    [0, 39770.35, 55368.28],
    [0, 40006.27, 54754.89],
    [0, 40181.96, 54041.1],
    [0, 40134.78, 52955.86],
    [0, 40087.59, 51870.62],
    [0, 40040.41, 50785.38],
    [0, 39993.22, 49700.14],
    [0, 39946.04, 48614.9],
    [0, 39936, 47641.1],
    [0, 39936, 46697.41],
    [0, 39936, 45753.73],
    [0, 39936, 44810.04],
    [0, 39936, 43866.35],
    [0, 39918.93, 42854.4],
    [0, 39871.75, 41721.98],
    [0, 39824.57, 40589.55],
    [0, 39777.38, 39457.13],
    [0, 39730.2, 38324.71],
    [0, 39683.01, 37192.28],
    [0, 39680, 36369.07],
    [0, 39680, 35566.93],
    [0, 39680, 34764.8],
    [0, 39680, 33962.67],
    [0, 39680, 33160.54],
    [0, 39680, 32527.06],
    [0, 39680, 32055.21],
    [0, 39680, 31583.37],
    [0, 39680, 31111.53],
    [0, 39680, 30639.69],
    [0, 39675.98, 30123.67],
    [0, 39628.8, 29132.8],
    [0, 39581.62, 28141.93],
    [0, 39534.43, 27151.06],
    [0, 39487.25, 26160.19],
    [0, 39440.06, 25169.32],
    [0, 39361.76, 24240.69],
    [0, 39267.39, 23344.19],
    [0, 39173.02, 22447.69],
    [0, 39078.65, 21551.18],
    [0, 38984.28, 20654.68],
    [0, 38923.04, 19835.48],
    [0, 38970.23, 19269.27],
    [0, 39017.41, 18703.06],
    [0, 39064.6, 18136.85],
    [0, 39111.78, 17570.63],
    [0, 39158.96, 17004.42],
    [0, 39435.04, 16781.55],
    [0, 39765.33, 16640],
    [0, 40095.62, 16498.45],
    [0, 40425.91, 16356.89],
    [0, 40756.2, 16215.34],
    [993.8823, 41122.64, 16073.79],
    [3589.02, 41547.29, 15932.24],
    [6184.157, 41971.95, 15790.68],
    [8779.294, 42396.61, 15649.13],
    [11374.43, 42821.27, 15507.58],
    [13969.57, 43245.93, 15366.02],
    [15796.71, 43715.77, 15224.47],
    [17589.71, 44187.61, 15082.92],
    [19382.71, 44659.45, 14941.36],
    [21175.72, 45131.29, 14799.81],
    [22968.72, 45603.14, 14658.26],
    [24686.43, 46100.08, 14516.71],
    [26337.88, 46619.11, 14375.15],
    [27989.33, 47138.13, 14233.6],
    [29640.79, 47657.16, 14092.05],
    [31292.23, 48176.19, 13950.49],
    [32933.65, 48705.25, 13798.9],
    [34490.73, 49318.65, 13562.98],
    [36047.81, 49932.05, 13327.06],
    [37604.89, 50545.44, 13091.14],
    [39161.98, 51158.84, 12855.22],
    [40719.06, 51772.23, 12619.29],
    [41922.76, 52225, 12415.5],
    [42960.82, 52602.48, 12226.76],
    [43998.87, 52979.95, 12038.02],
    [45036.93, 53357.43, 11849.29],
    [46074.98, 53734.9, 11660.55],
    [47293.74, 54196.71, 11411.58],
    [49039.56, 54904.47, 10986.92],
    [50785.38, 55612.23, 10562.26],
    [52531.2, 56320, 10137.6],
    [54277.02, 57027.77, 9712.941],
    [56022.84, 57735.53, 9288.282],
    [57494.59, 58325.84, 8785.317],
    [58910.12, 58892.05, 8266.29],
    [60325.65, 59458.26, 7747.263],
    [61741.18, 60024.47, 7228.235],
    [63156.71, 60590.68, 6709.208],
    [64076.3, 60470.21, 6457.224],
    [64265.04, 59337.79, 6598.776],
    [64453.77, 58205.36, 6740.33],
    [64642.51, 57072.94, 6881.882],
    [64831.25, 55940.52, 7023.435],
    [65019.98, 54808.09, 7164.988],
    [64746.92, 53260.05, 7398.902],
    [64463.81, 51702.96, 7634.824],
    [64180.71, 50145.88, 7870.745],
    [63897.6, 48588.8, 8106.667],
    [63614.49, 47031.72, 8342.588],
    [63592.41, 45605.14, 8474.102],
    [63781.14, 44283.98, 8521.286],
    [63969.88, 42962.82, 8568.471],
    [64158.62, 41641.66, 8615.655],
    [64347.36, 40320.5, 8662.839],
    [64415.62, 38993.32, 8704],
    [63660.68, 37624.97, 8704],
    [62905.73, 36256.63, 8704],
    [62150.78, 34888.28, 8704],
    [61395.83, 33519.94, 8704],
    [60640.88, 32151.59, 8704],
    [60283.48, 30882.63, 8704],
    [60094.75, 29655.84, 8704],
    [59906.01, 28429.05, 8704],
    [59717.27, 27202.26, 8704],
    [59528.54, 25975.47, 8704],
    [59339.8, 24722.57, 8704],
    [59151.06, 23401.41, 8704],
    [58962.32, 22080.25, 8704],
    [58773.59, 20759.09, 8704],
    [58584.85, 19437.93, 8704],
    [58396.11, 18116.77, 8704],
    [58287.69, 17197.18, 8704],
    [58193.32, 16347.86, 8704],
    [58098.95, 15498.54, 8704],
    [58004.58, 14649.22, 8704],
    [57910.21, 13799.91, 8704],
    [57795.77, 12267.92, 8704],
    [57654.21, 9814.337, 8704],
    [57512.66, 7360.753, 8704],
    [57371.11, 4907.168, 8704],
    [57229.55, 2453.584, 8704],
    [57088, 0, 8704]])
# Create the array from the provided data


def create_custom_colorscale(rgb_array):
    """Convert RGB array to Plotly colorscale format"""
    n_colors = len(rgb_array)
    colorscale = []
    for i, rgb in enumerate(rgb_array):
        position = i / (n_colors - 1)
        color = f'rgb({int(rgb[0]/255)}, {int(rgb[1]/255)}, {int(rgb[2]/255)})'
        colorscale.append([position, color])
    return colorscale

# Create colorscales
rainbowlightct = create_custom_colorscale(igor_data)
COLORSCALES = {
    'Custom Rainbow': rainbowlightct,
    'Viridis': 'Viridis',
    'Plasma': 'Plasma',
    'Inferno': 'Inferno',
    'Magma': 'Magma',
    'Cividis': 'Cividis',
    'Turbo': 'Turbo'
}

def create_latex_labels():
    """Create LaTeX-style labels for plots"""
    return {
        'kx': 'k<sub>x</sub> (Å<sup>-1</sup>)',
        'ky': 'k<sub>y</sub> (Å<sup>-1</sup>)',
        'kz': 'k<sub>z</sub> (Å<sup>-1</sup>)',
        'E_binding': 'E<sub>b</sub> (eV)',
        'E_kinetic': 'E<sub>k</sub> (eV)',
        'intensity': 'Intensity (arb. units)',
        'normalized_intensity': 'Normalized Intensity',
        'binding_energy': 'Binding Energy (eV)',
        'kinetic_energy': 'Kinetic Energy (eV)',
        'photon_energy': 'hν (eV)',
        'work_function': 'Φ (eV)',
        'angle': 'θ (°)',
        'polar_angle': 'φ (°)',
        'azimuthal_angle': 'α (°)'
    }

class PlotWindow:
    """Dedicated plot viewing window"""

    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.plot_file = None

    def create_window(self):
        """Create the plot window"""
        if self.window is None or not self.window.winfo_exists():
            self.window = tk.Toplevel(self.parent)
            self.window.title("📊 ARPES Plot Viewer")
            self.window.geometry("1200x800")

            # Create main frame
            main_frame = ttk.Frame(self.window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Title
            title_label = ttk.Label(main_frame, text="📊 Interactive ARPES Plot",
                                   font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 10))

            # Control buttons
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(0, 10))

            refresh_btn = ttk.Button(button_frame, text="🔄 Refresh",
                                   command=self.refresh_plot)
            refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

            browser_btn = ttk.Button(button_frame, text="🌐 Open in Browser",
                                   command=self.open_in_browser)
            browser_btn.pack(side=tk.LEFT, padx=(0, 10))

            close_btn = ttk.Button(button_frame, text="❌ Close",
                                 command=self.close_window)
            close_btn.pack(side=tk.RIGHT)

            # Plot display area
            self.plot_frame = ttk.LabelFrame(main_frame, text="Plot Display", padding=10)
            self.plot_frame.pack(fill=tk.BOTH, expand=True)

            # Try different approaches for displaying HTML
            self.setup_plot_display()

            # Handle window closing
            self.window.protocol("WM_DELETE_WINDOW", self.close_window)

        return self.window

    def setup_plot_display(self):
        """Setup the plot display area"""
        # Create a text widget to show plot info
        self.info_text = tk.Text(self.plot_frame, height=3, wrap=tk.WORD)
        self.info_text.pack(fill=tk.X, pady=(0, 10))
        self.info_text.insert(tk.END, "Plot will be displayed here. Use 'Refresh' to update the plot.\n")
        self.info_text.insert(tk.END, "For full interactivity, use 'Open in Browser' button.\n")
        self.info_text.insert(tk.END, "Plot file location will be shown below when available.")
        self.info_text.config(state=tk.DISABLED)

        # Create a frame for plot preview (we'll add an image preview)
        self.preview_frame = ttk.Frame(self.plot_frame)
        self.preview_frame.pack(fill=tk.BOTH, expand=True)

        # Add a label to show plot file path
        self.file_label = ttk.Label(self.preview_frame, text="No plot file loaded",
                                   foreground="gray")
        self.file_label.pack(pady=10)

        # Add a canvas for plot preview (if we can implement it)
        self.canvas_frame = ttk.Frame(self.preview_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Add instructions
        instructions = ttk.Label(self.preview_frame,
                               text="📋 Instructions:\n" +
                                    "• Generate a plot in the main window\n" +
                                    "• Plot info will appear above\n" +
                                    "• Use 'Open in Browser' for full interactivity\n" +
                                    "• Enable 'Live Plot Updates' for automatic refresh",
                               justify=tk.LEFT, foreground="blue")
        instructions.pack(pady=10)

    def update_plot(self, plot_file_path):
        """Update the plot display"""
        self.plot_file = plot_file_path

        if self.window and self.window.winfo_exists():
            # Automatically update the display
            self.update_plot_display()

    def refresh_plot(self):
        """Refresh the plot display"""
        if self.plot_file and os.path.exists(self.plot_file):
            # Update the display without opening browser
            self.update_plot_display()
        else:
            messagebox.showwarning("No Plot", "No plot file available. Generate a plot first.")

    def update_plot_display(self):
        """Update the plot display in the window"""
        if self.plot_file and os.path.exists(self.plot_file):
            # Read the HTML file and extract some info
            try:
                with open(self.plot_file, 'r') as f:
                    content = f.read()

                # Try to extract plot title from HTML
                plot_title = "ARPES Plot"
                if '<title>' in content:
                    start = content.find('<title>') + 7
                    end = content.find('</title>')
                    if end > start:
                        plot_title = content[start:end]

                # Extract some plot info from the HTML
                plot_info = ""
                if 'E vs kx' in content:
                    plot_info = "📊 Type: Energy vs kx dispersion"
                elif 'kx vs ky' in content:
                    plot_info = "📊 Type: kx vs ky constant energy map"
                elif 'kx vs kz' in content:
                    plot_info = "📊 Type: kx vs kz constant energy map"
                else:
                    plot_info = "📊 Type: ARPES plot"

                # Update info text
                self.info_text.config(state=tk.NORMAL)
                self.info_text.delete(1.0, tk.END)
                self.info_text.insert(tk.END, f"✅ Plot loaded: {os.path.basename(self.plot_file)}\n")
                self.info_text.insert(tk.END, f"{plot_info}\n")
                self.info_text.insert(tk.END, f"📁 Location: {self.plot_file}\n")
                self.info_text.insert(tk.END, f"📊 File size: {len(content)/1024:.1f} KB\n")
                self.info_text.insert(tk.END, "🔄 Plot is ready! Click 'Open in Browser' for full interactivity.")
                self.info_text.config(state=tk.DISABLED)

                # Update file label with plot type
                self.file_label.config(text=f"✅ {plot_info.replace('📊 Type: ', '')}: {os.path.basename(self.plot_file)}")

                # Clear canvas frame and add a simple preview message
                for widget in self.canvas_frame.winfo_children():
                    widget.destroy()

                preview_label = ttk.Label(self.canvas_frame,
                                        text=f"📈 {plot_title}\n\n" +
                                             f"{plot_info}\n\n" +
                                             "Click 'Open in Browser' to view the interactive plot\n" +
                                             "with zoom, pan, and hover capabilities.",
                                        justify=tk.CENTER, foreground="darkblue",
                                        font=('Arial', 10))
                preview_label.pack(expand=True)

            except Exception as e:
                self.info_text.config(state=tk.NORMAL)
                self.info_text.delete(1.0, tk.END)
                self.info_text.insert(tk.END, f"❌ Error reading plot file: {e}")
                self.info_text.config(state=tk.DISABLED)

    def open_in_browser(self):
        """Open plot in browser - try to reuse existing tab"""
        if self.plot_file and os.path.exists(self.plot_file):
            # Try to use a specific browser controller to reuse tabs
            try:
                # Get the default browser
                browser = webbrowser.get()
                # Open with new=2 to try to reuse existing window/tab
                browser.open('file://' + self.plot_file, new=0)  # new=0 tries to reuse existing window
            except:
                # Fallback to regular open
                webbrowser.open('file://' + self.plot_file, new=0)
        else:
            messagebox.showwarning("No Plot", "No plot file available. Generate a plot first.")

    def close_window(self):
        """Close the plot window"""
        if self.window:
            self.window.destroy()
            self.window = None

    def show(self):
        """Show the plot window"""
        window = self.create_window()
        window.lift()
        window.focus_force()
        return window


class ARPES3DAnalyzer:
    """Advanced 3D ARPES analysis functionality"""

    def __init__(self, data_loader, progress_callback=None):
        self.data_loader = data_loader
        self.progress_callback = progress_callback
        self.labels = create_latex_labels()
        self.processed_data = None

    def update_progress(self, value, message=""):
        """Update progress bar if callback is provided"""
        if self.progress_callback:
            self.progress_callback(value, message)

    def process_threshold_and_filter(self, threshold=0.1, window_size=3,
                                   min_periods=1, suppress_at='max'):
        """Process data with threshold filtering and smoothing"""
        if not self.data_loader.data_proc:
            return None, "No data loaded"

        self.update_progress(10, "Converting data to 3D format...")

        # Convert data to 3D format
        all_data = []
        for i, df in enumerate(self.data_loader.data_proc):
            hv = self.data_loader.data_attributes[i]['hv']

            for energy_idx, energy in enumerate(df.index):
                for angle_idx, angle in enumerate(df.columns):
                    intensity = df.iloc[energy_idx, angle_idx]

                    # Calculate momentum
                    theta_rad = np.deg2rad(float(angle))
                    E_kinetic = hv - WORK_FUNCTION - energy
                    if E_kinetic > 0:
                        k_magnitude = 0.5123 * np.sqrt(E_kinetic)
                        kx = k_magnitude * np.sin(theta_rad)
                        ky = 0.0  # Assuming normal emission

                        all_data.append([energy, kx, ky, intensity])

        self.update_progress(30, "Creating DataFrame...")
        df = pd.DataFrame(all_data, columns=['binding_energy', 'kx', 'ky', 'intensity'])

        # Clean data if stored as lists
        if df.applymap(lambda x: isinstance(x, list)).any().any():
            df = df.applymap(lambda y: y[0] if isinstance(y, list) else y)

        self.update_progress(50, "Applying threshold and normalization...")

        # Normalize and threshold
        df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
            lambda x: x/x.max() if x.max() > 0 else 0)
        df.loc[df['intensity'] < threshold, 'intensity'] = 0

        self.update_progress(70, "Applying smoothing...")

        # Apply processing
        df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
            lambda x: x.rolling(window_size, min_periods=min_periods,
                               center=True).mean().fillna(0))

        # Normalize and threshold again
        df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
            lambda x: x/x.max() if x.max() > 0 else 0)
        df.loc[df['intensity'] < threshold, 'intensity'] = 0

        self.update_progress(90, "Applying energy suppression...")

        # Create a mask for non-zero intensities
        nonzero_mask = df['intensity'] > 0

        # Group by kx, ky coordinates
        coords_group = df[nonzero_mask].groupby(['kx', 'ky'])

        # Find the binding energy to keep for each coordinate
        if suppress_at == 'max':
            energy_to_keep_sub = coords_group['binding_energy'].transform('max')
        elif suppress_at == 'min':
            energy_to_keep_sub = coords_group['binding_energy'].transform('min')
        elif suppress_at == 'none':
            # Return processed data without suppression
            self.processed_data = df.set_index(['binding_energy', 'kx', 'ky'])
            self.update_progress(100, "Processing complete!")
            return self.processed_data, "Data processed successfully"
        else:
            energy_to_keep_sub = coords_group['binding_energy'].transform('min')

        # Reindex the result from the non-zero subset to the full DataFrame index
        energy_to_keep = pd.Series(np.nan, index=df.index)
        energy_to_keep.loc[nonzero_mask] = energy_to_keep_sub

        # Create a boolean mask for points to keep
        keep_mask = (df['binding_energy'] == energy_to_keep) & nonzero_mask

        # Set intensity to 0 for all points except those we want to keep
        df.loc[~keep_mask, 'intensity'] = 0

        # Return processed data
        self.processed_data = df.set_index(['binding_energy', 'kx', 'ky'])
        self.update_progress(100, "Processing complete!")

        return self.processed_data, "Data processed successfully"


class ARPESDataLoader:
    """Data loading and processing class"""
    
    def __init__(self):
        self.data = None
        self.data_attributes = None
        self.data_proc = None
        self.work_function = WORK_FUNCTION
        
    def load_pxt_files(self, folder_path=None):
        """Load PXT files from a folder"""
        if folder_path is None:
            folder_path = filedialog.askdirectory(
                title="Select folder containing PXT files"
            )
        
        if not folder_path:
            return False, "No folder selected"

        pxt_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
        if not pxt_files:
            return False, "No PXT files found in the selected folder"

        pxt_files.sort()
        data_arrays = []
        attributes = []
        failed_files = []
        
        for i, file in enumerate(pxt_files):
            file_path = os.path.join(folder_path, file)
            try:
                data = read_single_pxt(file_path)
                df = pd.DataFrame(
                    data.values, 
                    columns=data.coords['phi'].values, 
                    index=data.coords['eV'].values
                )
                data_arrays.append(df)
                attributes.append(data.attrs)
            except Exception as e:
                failed_files.append((file, str(e)))

        if not data_arrays:
            return False, "Failed to load any PXT files"

        self.data = data_arrays
        self.data_attributes = attributes
        self._process_data()
        
        success_msg = f"Successfully loaded {len(data_arrays)} PXT files"
        if failed_files:
            success_msg += f"\nFailed to load {len(failed_files)} files"
        
        return True, success_msg
    
    def _process_data(self):
        """Process data to convert to binding energy"""
        if not self.data or not self.data_attributes:
            return
            
        self.data_proc = [df.copy() for df in self.data]
        
        for i in range(len(self.data_proc)):
            hv = self.data_attributes[i]['hv']
            new_index = [hv - self.work_function - abs(idx) for idx in self.data[i].index]
            self.data_proc[i] = self.data_proc[i].set_index(pd.Index(new_index))
            self.data_proc[i].attrs = {'polar': self.data_attributes[i]['polar']}
    
    def get_binding_energy_range(self):
        """Get the full binding energy range across all data"""
        if not self.data_proc:
            return 0, 1
            
        E_binding_values = []
        for i, df in enumerate(self.data_proc):
            hv = self.data_attributes[i]['hv']
            E_kinetic_values = df.index.values.astype(float)
            E_binding_scan = hv - self.work_function - E_kinetic_values
            E_binding_values.extend(E_binding_scan)
        
        E_binding_array = np.array(E_binding_values)
        return E_binding_array.min(), E_binding_array.max()

class ARPESPlotter:
    """Plotting class for ARPES data"""
    
    def __init__(self, data_loader):
        self.data_loader = data_loader
        self.current_fig = None
        
    def moving_average(self, data, kernel_size):
        """Apply moving average filter"""
        if kernel_size <= 1:
            return data
        kernel = np.ones(kernel_size) / kernel_size
        return np.convolve(data, kernel, mode='same')

    def find_peaks_in_intensity(self, intensities, neighborhood_size, threshold, smoothing_sigma):
        """Find peaks in intensity data"""
        intensities_smooth = gaussian_filter(intensities, sigma=smoothing_sigma)
        local_max = maximum_filter(intensities_smooth, size=neighborhood_size) == intensities_smooth
        detected_peaks = (intensities_smooth > threshold) & local_max
        peak_indices = np.argwhere(detected_peaks)
        return peak_indices

    def find_critical_points(self, intensity_grid, sigma=1.0):
        """Find critical points using Hessian analysis"""
        smoothed = gaussian_filter(intensity_grid, sigma=sigma)
        dy, dx = np.gradient(smoothed)
        dyy, dxy = np.gradient(dy)
        dxx, _ = np.gradient(dx)

        hessian_det = dxx * dyy - dxy**2
        maxima = (dxx < 0) & (dyy < 0) & (hessian_det > 0)
        minima = (dxx > 0) & (dyy > 0) & (hessian_det > 0)
        saddles = hessian_det < 0

        return maxima, minima, saddles

    def compute_euler_characteristic(self, intensities, threshold):
        """Compute Euler characteristic from binary intensity data"""
        binary_intensity = intensities >= threshold
        return euler_number(binary_intensity)
        
    def plot_E_vs_kx(self, scan_number, vmin, vmax, kernel_size, x_offset, y_offset,
                     colorscale='Custom Rainbow', show_peaks=False, peak_threshold=0.5,
                     neighborhood_size=5, smoothing_sigma=1.0, show_edges=False,
                     show_components=False, canny_sigma=1.0, canny_low=0.1, canny_high=0.3,
                     euler_threshold=0.5, show_euler_points=False, show_critical_points=False):
        """Plot binding energy vs kx for a single scan"""
        
        if not self.data_loader.data_proc:
            return None, "No data loaded"
            
        if scan_number < 0 or scan_number >= len(self.data_loader.data_proc):
            return None, f"Invalid scan number. Must be between 0 and {len(self.data_loader.data_proc)-1}"

        # Get data for the selected scan
        df = self.data_loader.data_proc[scan_number]
        hv = self.data_loader.data_attributes[scan_number]['hv']

        # Emission angles and kinetic energies
        emission_angles = df.columns.values.astype(float)
        theta_rad = np.deg2rad(emission_angles)
        E_kinetic_values = df.index.values.astype(float)

        # Ensure increasing order
        if np.any(np.diff(E_kinetic_values) < 0):
            E_kinetic_values = E_kinetic_values[::-1]
            df = df.iloc[::-1]

        # Create grids
        theta_grid, E_kinetic_grid = np.meshgrid(theta_rad, E_kinetic_values, indexing='xy')
        E_binding_grid = hv - WORK_FUNCTION - E_kinetic_grid + y_offset
        kx_grid = 0.5123 * np.sqrt(E_kinetic_grid) * np.sin(theta_grid) + x_offset

        # Process intensities
        intensities = df.values
        if kernel_size > 1:
            intensities = np.apply_along_axis(
                lambda m: self.moving_average(m, kernel_size), axis=0, arr=intensities
            )

        # Normalize intensities
        max_intensity = np.nanmax(intensities)
        if max_intensity > 0:
            intensities = intensities / max_intensity

        # Mask invalid data
        valid_mask = np.isfinite(kx_grid) & np.isfinite(E_binding_grid) & np.isfinite(intensities)
        intensities[~valid_mask] = np.nan

        # Create Plotly figure
        fig = go.Figure()

        # Add heatmap
        colorscale_to_use = COLORSCALES.get(colorscale, 'Viridis')
        
        fig.add_trace(go.Heatmap(
            x=kx_grid[0, :],
            y=E_binding_grid[:, 0],
            z=intensities,
            colorscale=colorscale_to_use,
            zmin=vmin,
            zmax=vmax,
            colorbar=dict(title="Intensity"),
            hovertemplate='kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<br>I: %{z:.3f}<extra></extra>'
        ))

        # Prepare intensities for analysis
        intensities_filled = np.nan_to_num(intensities, nan=0.0)

        # Add peaks if requested
        if show_peaks:
            peak_indices = self.find_peaks_in_intensity(
                intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
            )

            if peak_indices.size > 0:
                peak_kx = [kx_grid[i, j] for i, j in peak_indices]
                peak_E = [E_binding_grid[i, j] for i, j in peak_indices]

                fig.add_trace(go.Scatter(
                    x=peak_kx,
                    y=peak_E,
                    mode='markers',
                    marker=dict(color='red', size=8, symbol='circle'),
                    name='Peaks',
                    hovertemplate='Peak<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                ))

        # Add edge detection if requested
        if show_edges or show_components:
            edges = canny(intensities_filled, sigma=canny_sigma,
                         low_threshold=canny_low, high_threshold=canny_high)

            if show_edges:
                # Find edge coordinates
                edge_coords = np.where(edges)
                if len(edge_coords[0]) > 0:
                    edge_kx = [kx_grid[i, j] for i, j in zip(edge_coords[0], edge_coords[1])]
                    edge_E = [E_binding_grid[i, j] for i, j in zip(edge_coords[0], edge_coords[1])]

                    fig.add_trace(go.Scatter(
                        x=edge_kx,
                        y=edge_E,
                        mode='markers',
                        marker=dict(color='cyan', size=2, symbol='circle'),
                        name='Edges',
                        hovertemplate='Edge<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                    ))

            if show_components:
                labeled_array, num_features = label(edges, connectivity=2, return_num=True)

                for region_label in range(1, min(num_features + 1, 10)):  # Limit to 10 components
                    component_mask = labeled_array == region_label
                    component_coords = np.where(component_mask)

                    if len(component_coords[0]) > 0:
                        comp_kx = [kx_grid[i, j] for i, j in zip(component_coords[0], component_coords[1])]
                        comp_E = [E_binding_grid[i, j] for i, j in zip(component_coords[0], component_coords[1])]

                        fig.add_trace(go.Scatter(
                            x=comp_kx,
                            y=comp_E,
                            mode='markers',
                            marker=dict(color='yellow', size=1, symbol='circle'),
                            name=f'Component {region_label}',
                            hovertemplate=f'Component {region_label}<br>kx: %{{x:.3f}} Å⁻¹<br>E_B: %{{y:.3f}} eV<extra></extra>'
                        ))

        # Add Euler characteristic analysis
        if show_euler_points:
            binary_intensity = intensities_filled >= euler_threshold
            euler_char = self.compute_euler_characteristic(intensities_filled, euler_threshold)

            # Find binary points
            binary_coords = np.where(binary_intensity)
            if len(binary_coords[0]) > 0:
                euler_kx = [kx_grid[i, j] for i, j in zip(binary_coords[0], binary_coords[1])]
                euler_E = [E_binding_grid[i, j] for i, j in zip(binary_coords[0], binary_coords[1])]

                fig.add_trace(go.Scatter(
                    x=euler_kx,
                    y=euler_E,
                    mode='markers',
                    marker=dict(color='white', size=1, symbol='circle',
                               line=dict(color='black', width=0.5)),
                    name=f'Euler Points (χ={euler_char})',
                    hovertemplate=f'Euler Point<br>kx: %{{x:.3f}} Å⁻¹<br>E_B: %{{y:.3f}} eV<br>χ={euler_char}<extra></extra>'
                ))

        # Add critical points if requested
        if show_critical_points:
            maxima, minima, saddles = self.find_critical_points(intensities_filled, sigma=smoothing_sigma)

            # Plot maxima
            max_coords = np.where(maxima)
            if len(max_coords[0]) > 0:
                max_kx = [kx_grid[i, j] for i, j in zip(max_coords[0], max_coords[1])]
                max_E = [E_binding_grid[i, j] for i, j in zip(max_coords[0], max_coords[1])]

                fig.add_trace(go.Scatter(
                    x=max_kx,
                    y=max_E,
                    mode='markers',
                    marker=dict(color='green', size=8, symbol='triangle-up'),
                    name='Maxima',
                    hovertemplate='Maximum<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                ))

            # Plot minima
            min_coords = np.where(minima)
            if len(min_coords[0]) > 0:
                min_kx = [kx_grid[i, j] for i, j in zip(min_coords[0], min_coords[1])]
                min_E = [E_binding_grid[i, j] for i, j in zip(min_coords[0], min_coords[1])]

                fig.add_trace(go.Scatter(
                    x=min_kx,
                    y=min_E,
                    mode='markers',
                    marker=dict(color='blue', size=8, symbol='triangle-down'),
                    name='Minima',
                    hovertemplate='Minimum<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                ))

            # Plot saddles
            saddle_coords = np.where(saddles)
            if len(saddle_coords[0]) > 0:
                saddle_kx = [kx_grid[i, j] for i, j in zip(saddle_coords[0], saddle_coords[1])]
                saddle_E = [E_binding_grid[i, j] for i, j in zip(saddle_coords[0], saddle_coords[1])]

                fig.add_trace(go.Scatter(
                    x=saddle_kx,
                    y=saddle_E,
                    mode='markers',
                    marker=dict(color='orange', size=8, symbol='diamond'),
                    name='Saddles',
                    hovertemplate='Saddle<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                ))

        # Update layout with LaTeX-style labels
        labels = create_latex_labels()
        fig.update_layout(
            title=f'E<sub>b</sub> vs k<sub>x</sub> - Scan {scan_number}',
            xaxis_title=labels['kx'],
            yaxis_title=labels['E_binding'],
            width=800,
            height=600,
            yaxis=dict(autorange='reversed'),
            template='plotly_white'
        )

        self.current_fig = fig
        return fig, "Plot generated successfully"
    
    def plot_kx_vs_ky(self, E_binding, vmin, vmax, kernel_size, x_offset, y_offset,
                      colorscale='Custom Rainbow', use_contours=False, contour_levels=20,
                      show_peaks=False, peak_threshold=0.5, neighborhood_size=5, smoothing_sigma=1.0,
                      show_edges=False, show_components=False, canny_sigma=1.0, canny_low=0.1,
                      canny_high=0.3, euler_threshold=0.5, show_euler_points=False,
                      show_critical_points=False):
        """Plot kx vs ky constant energy map"""
        
        if not self.data_loader.data_proc:
            return None, "No data loaded"

        # Collect data from all scans
        kx_list, ky_list, intensity_list = [], [], []

        for i in range(len(self.data_loader.data_proc)):
            df = self.data_loader.data_proc[i]
            hv = self.data_loader.data_attributes[i]['hv']
            polar_angle = self.data_loader.data_attributes[i]['polar']
            polar_angle_rad = np.deg2rad(polar_angle)

            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)

            # Calculate kinetic energy for desired binding energy
            E_kinetic = hv - WORK_FUNCTION - E_binding
            E_kinetic_values = df.index.values.astype(float)
            
            if E_kinetic < E_kinetic_values.min() or E_kinetic > E_kinetic_values.max():
                continue

            k_magnitude = 0.5123 * np.sqrt(E_kinetic)
            kx = k_magnitude * np.sin(theta_rad) + x_offset
            ky = k_magnitude * np.sin(polar_angle_rad) + y_offset

            # Extract intensities
            if E_kinetic in E_kinetic_values:
                intensities = df.loc[E_kinetic].values
            else:
                intensities = df.apply(
                    lambda col: np.interp(E_kinetic, E_kinetic_values, col.values)
                ).values

            if kernel_size > 1:
                intensities = self.moving_average(intensities, kernel_size)

            kx_list.extend(kx)
            ky_list.extend(np.full_like(kx, ky))
            intensity_list.extend(intensities)

        if not kx_list:
            return None, f"No data available for binding energy {E_binding:.2f} eV"

        # Convert to arrays and interpolate
        kx_array = np.array(kx_list)
        ky_array = np.array(ky_list)
        intensity_array = np.array(intensity_list)

        # Create grid for interpolation
        grid_resolution = 150
        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)
        ky_grid = np.linspace(ky_array.min(), ky_array.max(), grid_resolution)
        kx_mesh, ky_mesh = np.meshgrid(kx_grid, ky_grid)

        intensity_grid = griddata(
            points=(kx_array, ky_array),
            values=intensity_array,
            xi=(kx_mesh, ky_mesh),
            method='cubic'
        )

        intensity_grid = np.nan_to_num(intensity_grid)
        max_intensity = intensity_grid.max()
        if max_intensity > 0:
            intensity_grid /= max_intensity

        # Create figure
        fig = go.Figure()
        colorscale_to_use = COLORSCALES.get(colorscale, 'Viridis')

        if use_contours:
            fig.add_trace(go.Contour(
                x=kx_grid,
                y=ky_grid,
                z=intensity_grid,
                colorscale=colorscale_to_use,
                ncontours=contour_levels,
                colorbar=dict(title="Intensity"),
                hovertemplate='kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<br>I: %{z:.3f}<extra></extra>'
            ))
        else:
            fig.add_trace(go.Heatmap(
                x=kx_grid,
                y=ky_grid,
                z=intensity_grid,
                colorscale=colorscale_to_use,
                zmin=vmin,
                zmax=vmax,
                colorbar=dict(title="Intensity"),
                hovertemplate='kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<br>I: %{z:.3f}<extra></extra>'
            ))

        # Add analysis features similar to E vs kx plot
        intensities_filled = np.nan_to_num(intensity_grid, nan=0.0)

        # Add peaks if requested
        if show_peaks:
            peak_indices = self.find_peaks_in_intensity(
                intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
            )

            if peak_indices.size > 0:
                peak_kx_coords = [kx_grid[i] for i, j in peak_indices]
                peak_ky_coords = [ky_grid[j] for i, j in peak_indices]

                fig.add_trace(go.Scatter(
                    x=peak_kx_coords,
                    y=peak_ky_coords,
                    mode='markers',
                    marker=dict(color='red', size=8, symbol='circle'),
                    name='Peaks',
                    hovertemplate='Peak<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                ))

        # Add edge detection if requested
        if show_edges or show_components:
            edges = canny(intensities_filled, sigma=canny_sigma,
                         low_threshold=canny_low, high_threshold=canny_high)

            if show_edges:
                edge_coords = np.where(edges)
                if len(edge_coords[0]) > 0:
                    edge_kx_coords = [kx_grid[j] for i, j in zip(edge_coords[0], edge_coords[1])]
                    edge_ky_coords = [ky_grid[i] for i, j in zip(edge_coords[0], edge_coords[1])]

                    fig.add_trace(go.Scatter(
                        x=edge_kx_coords,
                        y=edge_ky_coords,
                        mode='markers',
                        marker=dict(color='cyan', size=2, symbol='circle'),
                        name='Edges',
                        hovertemplate='Edge<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                    ))

            if show_components:
                labeled_array, num_features = label(edges, connectivity=2, return_num=True)

                for region_label in range(1, min(num_features + 1, 10)):
                    component_mask = labeled_array == region_label
                    component_coords = np.where(component_mask)

                    if len(component_coords[0]) > 0:
                        comp_kx_coords = [kx_grid[j] for i, j in zip(component_coords[0], component_coords[1])]
                        comp_ky_coords = [ky_grid[i] for i, j in zip(component_coords[0], component_coords[1])]

                        fig.add_trace(go.Scatter(
                            x=comp_kx_coords,
                            y=comp_ky_coords,
                            mode='markers',
                            marker=dict(color='yellow', size=1, symbol='circle'),
                            name=f'Component {region_label}',
                            hovertemplate=f'Component {region_label}<br>kx: %{{x:.3f}} Å⁻¹<br>ky: %{{y:.3f}} Å⁻¹<extra></extra>'
                        ))

        # Add Euler characteristic analysis
        if show_euler_points:
            binary_intensity = intensities_filled >= euler_threshold
            euler_char = self.compute_euler_characteristic(intensities_filled, euler_threshold)

            binary_coords = np.where(binary_intensity)
            if len(binary_coords[0]) > 0:
                euler_kx_coords = [kx_grid[j] for i, j in zip(binary_coords[0], binary_coords[1])]
                euler_ky_coords = [ky_grid[i] for i, j in zip(binary_coords[0], binary_coords[1])]

                fig.add_trace(go.Scatter(
                    x=euler_kx_coords,
                    y=euler_ky_coords,
                    mode='markers',
                    marker=dict(color='white', size=1, symbol='circle',
                               line=dict(color='black', width=0.5)),
                    name=f'Euler Points (χ={euler_char})',
                    hovertemplate=f'Euler Point<br>kx: %{{x:.3f}} Å⁻¹<br>ky: %{{y:.3f}} Å⁻¹<br>χ={euler_char}<extra></extra>'
                ))

        # Add critical points if requested
        if show_critical_points:
            maxima, minima, saddles = self.find_critical_points(intensities_filled, sigma=smoothing_sigma)

            # Plot maxima
            max_coords = np.where(maxima)
            if len(max_coords[0]) > 0:
                max_kx_coords = [kx_grid[j] for i, j in zip(max_coords[0], max_coords[1])]
                max_ky_coords = [ky_grid[i] for i, j in zip(max_coords[0], max_coords[1])]

                fig.add_trace(go.Scatter(
                    x=max_kx_coords,
                    y=max_ky_coords,
                    mode='markers',
                    marker=dict(color='green', size=8, symbol='triangle-up'),
                    name='Maxima',
                    hovertemplate='Maximum<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                ))

            # Plot minima
            min_coords = np.where(minima)
            if len(min_coords[0]) > 0:
                min_kx_coords = [kx_grid[j] for i, j in zip(min_coords[0], min_coords[1])]
                min_ky_coords = [ky_grid[i] for i, j in zip(min_coords[0], min_coords[1])]

                fig.add_trace(go.Scatter(
                    x=min_kx_coords,
                    y=min_ky_coords,
                    mode='markers',
                    marker=dict(color='blue', size=8, symbol='triangle-down'),
                    name='Minima',
                    hovertemplate='Minimum<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                ))

            # Plot saddles
            saddle_coords = np.where(saddles)
            if len(saddle_coords[0]) > 0:
                saddle_kx_coords = [kx_grid[j] for i, j in zip(saddle_coords[0], saddle_coords[1])]
                saddle_ky_coords = [ky_grid[i] for i, j in zip(saddle_coords[0], saddle_coords[1])]

                fig.add_trace(go.Scatter(
                    x=saddle_kx_coords,
                    y=saddle_ky_coords,
                    mode='markers',
                    marker=dict(color='orange', size=8, symbol='diamond'),
                    name='Saddles',
                    hovertemplate='Saddle<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                ))

        labels = create_latex_labels()
        fig.update_layout(
            title=f'k<sub>x</sub> vs k<sub>y</sub> at E<sub>b</sub> = {E_binding:.2f} eV',
            xaxis_title=labels['kx'],
            yaxis_title=labels['ky'],
            width=800,
            height=600,
            template='plotly_white'
        )

        self.current_fig = fig
        return fig, "Plot generated successfully"

    def plot_kx_vs_kz(self, E_binding, vmin, vmax, kernel_size, x_offset, y_offset,
                      colorscale='Custom Rainbow', use_contours=False, contour_levels=20,
                      show_peaks=False, peak_threshold=0.5, neighborhood_size=5, smoothing_sigma=1.0,
                      show_edges=False, show_components=False, canny_sigma=1.0, canny_low=0.1,
                      canny_high=0.3, euler_threshold=0.5, show_euler_points=False,
                      show_critical_points=False):
        """Plot kx vs kz constant energy map"""

        if not self.data_loader.data_proc:
            return None, "No data loaded"

        # Collect data from all scans
        kx_list, kz_list, intensity_list = [], [], []

        for i in range(len(self.data_loader.data_proc)):
            df = self.data_loader.data_proc[i]
            hv = self.data_loader.data_attributes[i]['hv']

            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)

            # Calculate kinetic energy for desired binding energy
            E_kinetic = hv - WORK_FUNCTION - E_binding
            E_kinetic_values = df.index.values.astype(float)

            if E_kinetic < E_kinetic_values.min() or E_kinetic > E_kinetic_values.max():
                continue

            k_magnitude = 0.5123 * np.sqrt(E_kinetic)
            kx = k_magnitude * np.sin(theta_rad) + x_offset

            # Calculate kz using inner potential V0
            kz = 0.5123 * np.sqrt(E_kinetic * np.cos(theta_rad)**2 + V0) + y_offset

            # Extract intensities
            if E_kinetic in E_kinetic_values:
                intensities = df.loc[E_kinetic].values
            else:
                intensities = df.apply(
                    lambda col: np.interp(E_kinetic, E_kinetic_values, col.values)
                ).values

            if kernel_size > 1:
                intensities = self.moving_average(intensities, kernel_size)

            kx_list.extend(kx)
            kz_list.extend(kz)
            intensity_list.extend(intensities)

        if not kx_list:
            return None, f"No data available for binding energy {E_binding:.2f} eV"

        # Convert to arrays and interpolate
        kx_array = np.array(kx_list)
        kz_array = np.array(kz_list)
        intensity_array = np.array(intensity_list)

        # Create grid for interpolation
        grid_resolution = 150
        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)
        kz_grid = np.linspace(kz_array.min(), kz_array.max(), grid_resolution)
        kx_mesh, kz_mesh = np.meshgrid(kx_grid, kz_grid)

        intensity_grid = griddata(
            points=(kx_array, kz_array),
            values=intensity_array,
            xi=(kx_mesh, kz_mesh),
            method='cubic'
        )

        intensity_grid = np.nan_to_num(intensity_grid)
        max_intensity = intensity_grid.max()
        if max_intensity > 0:
            intensity_grid /= max_intensity

        # Create figure
        fig = go.Figure()
        colorscale_to_use = COLORSCALES.get(colorscale, 'Viridis')

        if use_contours:
            fig.add_trace(go.Contour(
                x=kx_grid,
                y=kz_grid,
                z=intensity_grid,
                colorscale=colorscale_to_use,
                ncontours=contour_levels,
                colorbar=dict(title="Intensity"),
                hovertemplate='kx: %{x:.3f} Å⁻¹<br>kz: %{y:.3f} Å⁻¹<br>I: %{z:.3f}<extra></extra>'
            ))
        else:
            fig.add_trace(go.Heatmap(
                x=kx_grid,
                y=kz_grid,
                z=intensity_grid,
                colorscale=colorscale_to_use,
                zmin=vmin,
                zmax=vmax,
                colorbar=dict(title="Intensity"),
                hovertemplate='kx: %{x:.3f} Å⁻¹<br>kz: %{y:.3f} Å⁻¹<br>I: %{z:.3f}<extra></extra>'
            ))

        # Add analysis features (similar to kx vs ky)
        intensities_filled = np.nan_to_num(intensity_grid, nan=0.0)

        if show_peaks:
            peak_indices = self.find_peaks_in_intensity(
                intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
            )

            if peak_indices.size > 0:
                peak_kx_coords = [kx_grid[i] for i, j in peak_indices]
                peak_kz_coords = [kz_grid[j] for i, j in peak_indices]

                fig.add_trace(go.Scatter(
                    x=peak_kx_coords,
                    y=peak_kz_coords,
                    mode='markers',
                    marker=dict(color='red', size=8, symbol='circle'),
                    name='Peaks',
                    hovertemplate='Peak<br>kx: %{x:.3f} Å⁻¹<br>kz: %{y:.3f} Å⁻¹<extra></extra>'
                ))

        labels = create_latex_labels()
        fig.update_layout(
            title=f'k<sub>x</sub> vs k<sub>z</sub> at E<sub>b</sub> = {E_binding:.2f} eV',
            xaxis_title=labels['kx'],
            yaxis_title=labels['kz'],
            width=800,
            height=600,
            template='plotly_white'
        )

        self.current_fig = fig
        return fig, "Plot generated successfully"


class ARPESAnalysisGUI:
    """Main GUI application class"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔬 Enhanced ARPES Analysis Interface")
        self.root.geometry("900x1000")

        # Initialize data components
        self.data_loader = ARPESDataLoader()
        self.plotter = ARPESPlotter(self.data_loader)
        self.analyzer_3d = ARPES3DAnalyzer(self.data_loader, self.update_progress)

        # Create a consistent plot file for reuse
        self.plot_file = tempfile.NamedTemporaryFile(delete=False, suffix='.html',
                                                    prefix='arpes_plot_')
        self.plot_file.close()

        # Initialize plot window
        self.plot_window = PlotWindow(self.root)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_message = tk.StringVar(value="Ready")

        # Current settings
        self.settings = {
            'mode': 'E vs kx',
            'scan_number': 0,
            'E_binding': 0.0,
            'vmin': 0.0,
            'vmax': 1.0,
            'kernel_size': 1,
            'x_offset': 0.0,
            'y_offset': 0.0,
            'colorscale': 'Custom Rainbow',
            'show_peaks': False,
            'use_contours': False,
            'contour_levels': 20,
            'peak_threshold': 0.5,
            'neighborhood_size': 5,
            'smoothing_sigma': 1.0,
            'show_edges': False,
            'show_components': False,
            'canny_sigma': 1.0,
            'canny_low': 0.1,
            'canny_high': 0.3,
            'euler_threshold': 0.5,
            'show_euler_points': False,
            'show_critical_points': False,
            'live_update': False,
            'open_in_browser': False,
            'use_plot_window': True
        }

        self.setup_gui()

    def update_progress(self, value, message=""):
        """Update progress bar and message"""
        self.progress_var.set(value)
        self.progress_message.set(message)
        self.root.update_idletasks()

    def setup_gui(self):
        """Setup the GUI interface"""

        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = ttk.Label(main_frame, text="🔬 Enhanced ARPES Analysis Interface",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 10))

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.setup_2d_analysis_tab()
        self.setup_3d_analysis_tab()

        # Progress bar at the bottom
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(progress_frame, text="Progress:").pack(side=tk.LEFT)
        self.progress_bar = Progressbar(progress_frame, variable=self.progress_var,
                                       maximum=100, length=300)
        self.progress_bar.pack(side=tk.LEFT, padx=(10, 10))

        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_message)
        self.progress_label.pack(side=tk.LEFT)

    def setup_2d_analysis_tab(self):
        """Setup the 2D analysis tab"""
        tab_2d = ttk.Frame(self.notebook)
        self.notebook.add(tab_2d, text="2D Analysis")

        # Create scrollable frame for 2D analysis
        canvas = tk.Canvas(tab_2d)
        scrollbar = ttk.Scrollbar(tab_2d, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Move existing GUI elements to this tab
        self.setup_2d_controls(scrollable_frame)

    def setup_2d_controls(self, parent_frame):
        """Setup 2D analysis controls"""
        # Data loading section
        data_frame = ttk.LabelFrame(parent_frame, text="Data Loading", padding=10)
        data_frame.pack(fill=tk.X, pady=(0, 10))

        button_frame = ttk.Frame(data_frame)
        button_frame.pack(fill=tk.X)

        self.load_button = ttk.Button(button_frame, text="📁 Load PXT Files",
                                     command=self.load_data)
        self.load_button.pack(side=tk.LEFT, padx=(0, 10))

        self.export_button = ttk.Button(button_frame, text="💾 Export Plot",
                                       command=self.export_plot)
        self.export_button.pack(side=tk.LEFT)

        self.generate_button = ttk.Button(button_frame, text="🎨 Generate Plot",
                                         command=self.generate_plot)
        self.generate_button.pack(side=tk.LEFT, padx=(10, 0))

        self.plot_window_button = ttk.Button(button_frame, text="📊 Show Plot Window",
                                            command=self.show_plot_window)
        self.plot_window_button.pack(side=tk.LEFT, padx=(10, 0))

        self.status_label = ttk.Label(data_frame, text="Ready to load data",
                                     foreground="blue")
        self.status_label.pack(pady=(10, 0))

        # Add helpful info
        info_label = ttk.Label(data_frame,
                              text="💡 Tip: Use 'Show Plot Window' for dedicated plot viewing, enable 'Live Plot Updates' for real-time changes",
                              font=('Arial', 8), foreground="gray")
        info_label.pack(pady=(5, 0))

        # Plot settings section
        plot_frame = ttk.LabelFrame(parent_frame, text="Plot Settings", padding=10)
        plot_frame.pack(fill=tk.X, pady=(0, 10))

        # Mode and colorscale
        mode_frame = ttk.Frame(plot_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(mode_frame, text="Plot Mode:").pack(side=tk.LEFT)
        self.mode_var = tk.StringVar(value="E vs kx")
        mode_combo = ttk.Combobox(mode_frame, textvariable=self.mode_var,
                                 values=["E vs kx", "kx vs ky", "kx vs kz"], state="readonly", width=15)
        mode_combo.pack(side=tk.LEFT, padx=(10, 20))
        mode_combo.bind('<<ComboboxSelected>>', self.on_setting_change)

        ttk.Label(mode_frame, text="Colorscale:").pack(side=tk.LEFT)
        self.colorscale_var = tk.StringVar(value="Custom Rainbow")
        colorscale_combo = ttk.Combobox(mode_frame, textvariable=self.colorscale_var,
                                       values=list(COLORSCALES.keys()), state="readonly", width=15)
        colorscale_combo.pack(side=tk.LEFT, padx=(10, 0))
        colorscale_combo.bind('<<ComboboxSelected>>', self.on_setting_change)

        # Scan number and binding energy
        scan_frame = ttk.Frame(plot_frame)
        scan_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(scan_frame, text="Scan Number:").pack(side=tk.LEFT)
        self.scan_var = tk.IntVar(value=0)
        self.scan_scale = ttk.Scale(scan_frame, from_=0, to=0, variable=self.scan_var,
                                   orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        self.scan_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.scan_label = ttk.Label(scan_frame, text="0")
        self.scan_label.pack(side=tk.LEFT)

        energy_frame = ttk.Frame(plot_frame)
        energy_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(energy_frame, text="Binding Energy (eV):").pack(side=tk.LEFT)
        self.energy_var = tk.DoubleVar(value=0.0)
        self.energy_scale = ttk.Scale(energy_frame, from_=-2.0, to=2.0, variable=self.energy_var,
                                     orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        self.energy_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_label = ttk.Label(energy_frame, text="0.00")
        self.energy_label.pack(side=tk.LEFT)

        # Display controls section
        display_frame = ttk.LabelFrame(parent_frame, text="Display Controls", padding=10)
        display_frame.pack(fill=tk.X, pady=(0, 10))

        # Intensity range
        intensity_frame = ttk.Frame(display_frame)
        intensity_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(intensity_frame, text="Min Intensity:").pack(side=tk.LEFT)
        self.vmin_var = tk.DoubleVar(value=0.0)
        vmin_scale = ttk.Scale(intensity_frame, from_=0.0, to=1.0, variable=self.vmin_var,
                              orient=tk.HORIZONTAL, length=150, command=self.on_setting_change)
        vmin_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.vmin_label = ttk.Label(intensity_frame, text="0.00")
        self.vmin_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(intensity_frame, text="Max Intensity:").pack(side=tk.LEFT)
        self.vmax_var = tk.DoubleVar(value=1.0)
        vmax_scale = ttk.Scale(intensity_frame, from_=0.0, to=1.0, variable=self.vmax_var,
                              orient=tk.HORIZONTAL, length=150, command=self.on_setting_change)
        vmax_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.vmax_label = ttk.Label(intensity_frame, text="1.00")
        self.vmax_label.pack(side=tk.LEFT)

        # Offsets
        offset_frame = ttk.Frame(display_frame)
        offset_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(offset_frame, text="X Offset:").pack(side=tk.LEFT)
        self.x_offset_var = tk.DoubleVar(value=0.0)
        x_offset_scale = ttk.Scale(offset_frame, from_=-2.0, to=2.0, variable=self.x_offset_var,
                                  orient=tk.HORIZONTAL, length=150, command=self.on_setting_change)
        x_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.x_offset_label = ttk.Label(offset_frame, text="0.00")
        self.x_offset_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(offset_frame, text="Y Offset:").pack(side=tk.LEFT)
        self.y_offset_var = tk.DoubleVar(value=0.0)
        y_offset_scale = ttk.Scale(offset_frame, from_=-2.0, to=2.0, variable=self.y_offset_var,
                                  orient=tk.HORIZONTAL, length=150, command=self.on_setting_change)
        y_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.y_offset_label = ttk.Label(offset_frame, text="0.00")
        self.y_offset_label.pack(side=tk.LEFT)

        # Kernel size
        kernel_frame = ttk.Frame(display_frame)
        kernel_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(kernel_frame, text="Kernel Size:").pack(side=tk.LEFT)
        self.kernel_var = tk.IntVar(value=1)
        kernel_scale = ttk.Scale(kernel_frame, from_=1, to=21, variable=self.kernel_var,
                                orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        kernel_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.kernel_label = ttk.Label(kernel_frame, text="1")
        self.kernel_label.pack(side=tk.LEFT)

        # Analysis options section
        analysis_frame = ttk.LabelFrame(parent_frame, text="Analysis Options", padding=10)
        analysis_frame.pack(fill=tk.X, pady=(0, 10))

        # Checkboxes
        checkbox_frame = ttk.Frame(analysis_frame)
        checkbox_frame.pack(fill=tk.X, pady=(0, 5))

        self.show_peaks_var = tk.BooleanVar(value=False)
        peaks_check = ttk.Checkbutton(checkbox_frame, text="Show Peaks",
                                     variable=self.show_peaks_var, command=self.on_setting_change)
        peaks_check.pack(side=tk.LEFT, padx=(0, 20))

        self.use_contours_var = tk.BooleanVar(value=False)
        contours_check = ttk.Checkbutton(checkbox_frame, text="Use Contours",
                                        variable=self.use_contours_var, command=self.on_setting_change)
        contours_check.pack(side=tk.LEFT)

        # Analysis parameters
        contour_frame = ttk.Frame(analysis_frame)
        contour_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(contour_frame, text="Contour Levels:").pack(side=tk.LEFT)
        self.contour_var = tk.IntVar(value=20)
        contour_scale = ttk.Scale(contour_frame, from_=5, to=50, variable=self.contour_var,
                                 orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        contour_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.contour_label = ttk.Label(contour_frame, text="20")
        self.contour_label.pack(side=tk.LEFT)

        peak_frame = ttk.Frame(analysis_frame)
        peak_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(peak_frame, text="Peak Threshold:").pack(side=tk.LEFT)
        self.peak_threshold_var = tk.DoubleVar(value=0.5)
        peak_scale = ttk.Scale(peak_frame, from_=0.0, to=1.0, variable=self.peak_threshold_var,
                              orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        peak_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.peak_threshold_label = ttk.Label(peak_frame, text="0.50")
        self.peak_threshold_label.pack(side=tk.LEFT)

        neighbor_frame = ttk.Frame(analysis_frame)
        neighbor_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(neighbor_frame, text="Neighborhood Size:").pack(side=tk.LEFT)
        self.neighbor_var = tk.IntVar(value=5)
        neighbor_scale = ttk.Scale(neighbor_frame, from_=3, to=15, variable=self.neighbor_var,
                                  orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        neighbor_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.neighbor_label = ttk.Label(neighbor_frame, text="5")
        self.neighbor_label.pack(side=tk.LEFT)

        smooth_frame = ttk.Frame(analysis_frame)
        smooth_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(smooth_frame, text="Smoothing Sigma:").pack(side=tk.LEFT)
        self.smooth_var = tk.DoubleVar(value=1.0)
        smooth_scale = ttk.Scale(smooth_frame, from_=0.1, to=3.0, variable=self.smooth_var,
                                orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        smooth_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.smooth_label = ttk.Label(smooth_frame, text="1.00")
        self.smooth_label.pack(side=tk.LEFT)

        # Advanced analysis section
        advanced_frame = ttk.LabelFrame(parent_frame, text="Advanced Analysis", padding=10)
        advanced_frame.pack(fill=tk.X, pady=(0, 10))

        # Advanced checkboxes
        advanced_checkbox_frame = ttk.Frame(advanced_frame)
        advanced_checkbox_frame.pack(fill=tk.X, pady=(0, 5))

        self.show_edges_var = tk.BooleanVar(value=False)
        edges_check = ttk.Checkbutton(advanced_checkbox_frame, text="Show Edges",
                                     variable=self.show_edges_var, command=self.on_setting_change)
        edges_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_components_var = tk.BooleanVar(value=False)
        components_check = ttk.Checkbutton(advanced_checkbox_frame, text="Show Components",
                                          variable=self.show_components_var, command=self.on_setting_change)
        components_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_euler_var = tk.BooleanVar(value=False)
        euler_check = ttk.Checkbutton(advanced_checkbox_frame, text="Show Euler Points",
                                     variable=self.show_euler_var, command=self.on_setting_change)
        euler_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_critical_var = tk.BooleanVar(value=False)
        critical_check = ttk.Checkbutton(advanced_checkbox_frame, text="Show Critical Points",
                                        variable=self.show_critical_var, command=self.on_setting_change)
        critical_check.pack(side=tk.LEFT)

        # Canny edge detection parameters
        canny_frame = ttk.Frame(advanced_frame)
        canny_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(canny_frame, text="Canny Sigma:").pack(side=tk.LEFT)
        self.canny_sigma_var = tk.DoubleVar(value=1.0)
        canny_sigma_scale = ttk.Scale(canny_frame, from_=0.1, to=3.0, variable=self.canny_sigma_var,
                                     orient=tk.HORIZONTAL, length=120, command=self.on_setting_change)
        canny_sigma_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.canny_sigma_label = ttk.Label(canny_frame, text="1.00")
        self.canny_sigma_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(canny_frame, text="Low Threshold:").pack(side=tk.LEFT)
        self.canny_low_var = tk.DoubleVar(value=0.1)
        canny_low_scale = ttk.Scale(canny_frame, from_=0.0, to=1.0, variable=self.canny_low_var,
                                   orient=tk.HORIZONTAL, length=120, command=self.on_setting_change)
        canny_low_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.canny_low_label = ttk.Label(canny_frame, text="0.10")
        self.canny_low_label.pack(side=tk.LEFT)

        canny_frame2 = ttk.Frame(advanced_frame)
        canny_frame2.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(canny_frame2, text="High Threshold:").pack(side=tk.LEFT)
        self.canny_high_var = tk.DoubleVar(value=0.3)
        canny_high_scale = ttk.Scale(canny_frame2, from_=0.0, to=1.0, variable=self.canny_high_var,
                                    orient=tk.HORIZONTAL, length=120, command=self.on_setting_change)
        canny_high_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.canny_high_label = ttk.Label(canny_frame2, text="0.30")
        self.canny_high_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(canny_frame2, text="Euler Threshold:").pack(side=tk.LEFT)
        self.euler_threshold_var = tk.DoubleVar(value=0.5)
        euler_threshold_scale = ttk.Scale(canny_frame2, from_=0.0, to=1.0, variable=self.euler_threshold_var,
                                         orient=tk.HORIZONTAL, length=120, command=self.on_setting_change)
        euler_threshold_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.euler_threshold_label = ttk.Label(canny_frame2, text="0.50")
        self.euler_threshold_label.pack(side=tk.LEFT)

        # Live update checkbox
        live_frame = ttk.Frame(advanced_frame)
        live_frame.pack(fill=tk.X, pady=(5, 0))

        self.live_update_var = tk.BooleanVar(value=False)
        live_check = ttk.Checkbutton(live_frame, text="Live Plot Updates",
                                    variable=self.live_update_var, command=self.on_setting_change)
        live_check.pack(side=tk.LEFT, padx=(0, 20))

        self.open_in_browser_var = tk.BooleanVar(value=False)
        browser_check = ttk.Checkbutton(live_frame, text="Open in Browser",
                                       variable=self.open_in_browser_var, command=self.on_setting_change)
        browser_check.pack(side=tk.LEFT, padx=(0, 20))

        self.use_plot_window_var = tk.BooleanVar(value=True)
        plot_window_check = ttk.Checkbutton(live_frame, text="Use Plot Window",
                                          variable=self.use_plot_window_var, command=self.on_setting_change)
        plot_window_check.pack(side=tk.LEFT)

    def setup_3d_analysis_tab(self):
        """Setup the 3D analysis tab"""
        tab_3d = ttk.Frame(self.notebook)
        self.notebook.add(tab_3d, text="3D Analysis")

        # Create scrollable frame for 3D analysis
        canvas = tk.Canvas(tab_3d)
        scrollbar = ttk.Scrollbar(tab_3d, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 3D Analysis controls
        self.setup_3d_controls(scrollable_frame)

    def setup_3d_controls(self, parent_frame):
        """Setup 3D analysis controls"""

        # Data loading section for 3D
        data_3d_frame = ttk.LabelFrame(parent_frame, text="3D Data Loading", padding=10)
        data_3d_frame.pack(fill=tk.X, pady=(0, 10))

        button_3d_frame = ttk.Frame(data_3d_frame)
        button_3d_frame.pack(fill=tk.X)

        self.load_processed_button = ttk.Button(button_3d_frame, text="📂 Load Processed Data",
                                               command=self.load_processed_data)
        self.load_processed_button.pack(side=tk.LEFT, padx=(0, 10))

        self.save_processed_button = ttk.Button(button_3d_frame, text="💾 Save Processed Data",
                                               command=self.save_processed_data)
        self.save_processed_button.pack(side=tk.LEFT)

        # 3D Processing section
        processing_frame = ttk.LabelFrame(parent_frame, text="3D Data Processing", padding=10)
        processing_frame.pack(fill=tk.X, pady=(0, 10))

        # Processing parameters
        params_frame = ttk.Frame(processing_frame)
        params_frame.pack(fill=tk.X, pady=(0, 10))

        # Threshold
        threshold_frame = ttk.Frame(params_frame)
        threshold_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(threshold_frame, text="Threshold:").pack(side=tk.LEFT)
        self.threshold_3d_var = tk.DoubleVar(value=0.1)
        threshold_scale = ttk.Scale(threshold_frame, from_=0.01, to=0.5, variable=self.threshold_3d_var,
                                   orient=tk.HORIZONTAL, length=200, command=self.update_3d_labels)
        threshold_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.threshold_3d_label = ttk.Label(threshold_frame, text="0.10")
        self.threshold_3d_label.pack(side=tk.LEFT)

        # Window size
        window_frame = ttk.Frame(params_frame)
        window_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(window_frame, text="Window Size:").pack(side=tk.LEFT)
        self.window_size_var = tk.IntVar(value=3)
        window_scale = ttk.Scale(window_frame, from_=1, to=10, variable=self.window_size_var,
                                orient=tk.HORIZONTAL, length=200, command=self.update_3d_labels)
        window_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.window_size_label = ttk.Label(window_frame, text="3")
        self.window_size_label.pack(side=tk.LEFT)

        # Suppress at
        suppress_frame = ttk.Frame(params_frame)
        suppress_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(suppress_frame, text="Suppress at:").pack(side=tk.LEFT)
        self.suppress_var = tk.StringVar(value="max")
        suppress_combo = ttk.Combobox(suppress_frame, textvariable=self.suppress_var,
                                     values=["max", "min", "none"], state="readonly", width=10)
        suppress_combo.pack(side=tk.LEFT, padx=(10, 0))

        # Process button
        process_button = ttk.Button(processing_frame, text="🔄 Process 3D Data",
                                   command=self.process_3d_data)
        process_button.pack(pady=(10, 0))

        # 3D Visualization Parameters section
        viz_params_frame = ttk.LabelFrame(parent_frame, text="3D Visualization Parameters", padding=10)
        viz_params_frame.pack(fill=tk.X, pady=(0, 10))

        # Energy step and grid size
        energy_grid_frame = ttk.Frame(viz_params_frame)
        energy_grid_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(energy_grid_frame, text="Energy Step:").pack(side=tk.LEFT)
        self.energy_step_var = tk.DoubleVar(value=0.01)
        energy_step_scale = ttk.Scale(energy_grid_frame, from_=0.005, to=0.1, variable=self.energy_step_var,
                                     orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        energy_step_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_step_label = ttk.Label(energy_grid_frame, text="0.01")
        self.energy_step_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(energy_grid_frame, text="Grid Size:").pack(side=tk.LEFT)
        self.grid_size_var = tk.IntVar(value=200)
        grid_size_scale = ttk.Scale(energy_grid_frame, from_=50, to=500, variable=self.grid_size_var,
                                   orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        grid_size_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.grid_size_label = ttk.Label(energy_grid_frame, text="200")
        self.grid_size_label.pack(side=tk.LEFT)

        # Smoothing and surface threshold
        smooth_surface_frame = ttk.Frame(viz_params_frame)
        smooth_surface_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(smooth_surface_frame, text="Smoothing Sigma:").pack(side=tk.LEFT)
        self.smoothing_3d_var = tk.DoubleVar(value=1.3)
        smoothing_3d_scale = ttk.Scale(smooth_surface_frame, from_=0.1, to=5.0, variable=self.smoothing_3d_var,
                                      orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        smoothing_3d_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.smoothing_3d_label = ttk.Label(smooth_surface_frame, text="1.30")
        self.smoothing_3d_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(smooth_surface_frame, text="Surface Threshold:").pack(side=tk.LEFT)
        self.surface_threshold_var = tk.DoubleVar(value=0.2)
        surface_threshold_scale = ttk.Scale(smooth_surface_frame, from_=0.01, to=1.0, variable=self.surface_threshold_var,
                                           orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        surface_threshold_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.surface_threshold_label = ttk.Label(smooth_surface_frame, text="0.20")
        self.surface_threshold_label.pack(side=tk.LEFT)

        # 3D Offsets
        offsets_3d_frame = ttk.Frame(viz_params_frame)
        offsets_3d_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(offsets_3d_frame, text="kx Offset:").pack(side=tk.LEFT)
        self.kx_offset_var = tk.DoubleVar(value=0.0)
        kx_offset_scale = ttk.Scale(offsets_3d_frame, from_=-2.0, to=2.0, variable=self.kx_offset_var,
                                   orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        kx_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.kx_offset_label = ttk.Label(offsets_3d_frame, text="0.00")
        self.kx_offset_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(offsets_3d_frame, text="ky Offset:").pack(side=tk.LEFT)
        self.ky_offset_var = tk.DoubleVar(value=0.0)
        ky_offset_scale = ttk.Scale(offsets_3d_frame, from_=-2.0, to=2.0, variable=self.ky_offset_var,
                                   orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        ky_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.ky_offset_label = ttk.Label(offsets_3d_frame, text="0.00")
        self.ky_offset_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(offsets_3d_frame, text="Energy Offset:").pack(side=tk.LEFT)
        self.energy_offset_var = tk.DoubleVar(value=0.0)
        energy_offset_scale = ttk.Scale(offsets_3d_frame, from_=-2.0, to=2.0, variable=self.energy_offset_var,
                                       orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        energy_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_offset_label = ttk.Label(offsets_3d_frame, text="0.00")
        self.energy_offset_label.pack(side=tk.LEFT)

        # 3D Range Controls
        range_3d_frame = ttk.LabelFrame(parent_frame, text="3D Range Controls", padding=10)
        range_3d_frame.pack(fill=tk.X, pady=(0, 10))

        # Energy range
        energy_range_frame = ttk.Frame(range_3d_frame)
        energy_range_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(energy_range_frame, text="Energy Min:").pack(side=tk.LEFT)
        self.energy_min_var = tk.DoubleVar(value=-1.1)
        energy_min_scale = ttk.Scale(energy_range_frame, from_=-3.0, to=1.0, variable=self.energy_min_var,
                                    orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        energy_min_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_min_label = ttk.Label(energy_range_frame, text="-1.10")
        self.energy_min_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(energy_range_frame, text="Energy Max:").pack(side=tk.LEFT)
        self.energy_max_var = tk.DoubleVar(value=0.1)
        energy_max_scale = ttk.Scale(energy_range_frame, from_=-1.0, to=2.0, variable=self.energy_max_var,
                                    orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        energy_max_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_max_label = ttk.Label(energy_range_frame, text="0.10")
        self.energy_max_label.pack(side=tk.LEFT)

        # kx and ky range
        kx_range_frame = ttk.Frame(range_3d_frame)
        kx_range_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(kx_range_frame, text="kx Min:").pack(side=tk.LEFT)
        self.kx_min_var = tk.DoubleVar(value=-0.5)
        kx_min_scale = ttk.Scale(kx_range_frame, from_=-2.0, to=1.0, variable=self.kx_min_var,
                                orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        kx_min_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.kx_min_label = ttk.Label(kx_range_frame, text="-0.50")
        self.kx_min_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(kx_range_frame, text="kx Max:").pack(side=tk.LEFT)
        self.kx_max_var = tk.DoubleVar(value=0.5)
        kx_max_scale = ttk.Scale(kx_range_frame, from_=-1.0, to=2.0, variable=self.kx_max_var,
                                orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        kx_max_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.kx_max_label = ttk.Label(kx_range_frame, text="0.50")
        self.kx_max_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(kx_range_frame, text="ky Min:").pack(side=tk.LEFT)
        self.ky_min_var = tk.DoubleVar(value=-0.9)
        ky_min_scale = ttk.Scale(kx_range_frame, from_=-2.0, to=1.0, variable=self.ky_min_var,
                                orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        ky_min_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.ky_min_label = ttk.Label(kx_range_frame, text="-0.90")
        self.ky_min_label.pack(side=tk.LEFT)

        ky_range_frame = ttk.Frame(range_3d_frame)
        ky_range_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(ky_range_frame, text="ky Max:").pack(side=tk.LEFT)
        self.ky_max_var = tk.DoubleVar(value=0.1)
        ky_max_scale = ttk.Scale(ky_range_frame, from_=-1.0, to=2.0, variable=self.ky_max_var,
                                orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        ky_max_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.ky_max_label = ttk.Label(ky_range_frame, text="0.10")
        self.ky_max_label.pack(side=tk.LEFT)

        # 3D Surface Options
        surface_options_frame = ttk.LabelFrame(parent_frame, text="3D Surface Options", padding=10)
        surface_options_frame.pack(fill=tk.X, pady=(0, 10))

        # Surface mode and plane options
        surface_mode_frame = ttk.Frame(surface_options_frame)
        surface_mode_frame.pack(fill=tk.X, pady=(0, 5))

        self.single_color_var = tk.BooleanVar(value=True)
        single_color_check = ttk.Checkbutton(surface_mode_frame, text="Single Color Mode",
                                           variable=self.single_color_var)
        single_color_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_yellow_plane_var = tk.BooleanVar(value=False)
        yellow_plane_check = ttk.Checkbutton(surface_mode_frame, text="Show Yellow Plane",
                                           variable=self.show_yellow_plane_var)
        yellow_plane_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_red_plane_var = tk.BooleanVar(value=False)
        red_plane_check = ttk.Checkbutton(surface_mode_frame, text="Show Red Plane",
                                        variable=self.show_red_plane_var)
        red_plane_check.pack(side=tk.LEFT)

        # Plane energy values
        plane_energy_frame = ttk.Frame(surface_options_frame)
        plane_energy_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(plane_energy_frame, text="Yellow Plane Energy:").pack(side=tk.LEFT)
        self.yellow_plane_energy_var = tk.DoubleVar(value=0.0)
        yellow_plane_scale = ttk.Scale(plane_energy_frame, from_=-2.0, to=1.0, variable=self.yellow_plane_energy_var,
                                      orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        yellow_plane_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.yellow_plane_energy_label = ttk.Label(plane_energy_frame, text="0.00")
        self.yellow_plane_energy_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(plane_energy_frame, text="Red Plane Energy:").pack(side=tk.LEFT)
        self.red_plane_energy_var = tk.DoubleVar(value=-1.0)
        red_plane_scale = ttk.Scale(plane_energy_frame, from_=-2.0, to=1.0, variable=self.red_plane_energy_var,
                                   orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        red_plane_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.red_plane_energy_label = ttk.Label(plane_energy_frame, text="-1.00")
        self.red_plane_energy_label.pack(side=tk.LEFT)

        # 3D Visualization section
        viz_frame = ttk.LabelFrame(parent_frame, text="3D Visualization", padding=10)
        viz_frame.pack(fill=tk.X, pady=(0, 10))

        # Visualization buttons
        viz_buttons_frame = ttk.Frame(viz_frame)
        viz_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(viz_buttons_frame, text="📊 3D Scatter Plot",
                  command=self.plot_3d_scatter).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="🏔️ 3D Surface",
                  command=self.plot_3d_surface).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="📈 Projections",
                  command=self.plot_projections).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="🎯 Critical Points",
                  command=self.find_critical_points).pack(side=tk.LEFT)

    def update_labels(self):
        """Update all value labels"""
        self.scan_label.config(text=str(int(self.scan_var.get())))
        self.energy_label.config(text=f"{self.energy_var.get():.2f}")
        self.vmin_label.config(text=f"{self.vmin_var.get():.2f}")
        self.vmax_label.config(text=f"{self.vmax_var.get():.2f}")
        self.x_offset_label.config(text=f"{self.x_offset_var.get():.2f}")
        self.y_offset_label.config(text=f"{self.y_offset_var.get():.2f}")
        self.kernel_label.config(text=str(int(self.kernel_var.get())))
        self.contour_label.config(text=str(int(self.contour_var.get())))
        self.peak_threshold_label.config(text=f"{self.peak_threshold_var.get():.2f}")
        self.neighbor_label.config(text=str(int(self.neighbor_var.get())))
        self.smooth_label.config(text=f"{self.smooth_var.get():.2f}")
        self.canny_sigma_label.config(text=f"{self.canny_sigma_var.get():.2f}")
        self.canny_low_label.config(text=f"{self.canny_low_var.get():.2f}")
        self.canny_high_label.config(text=f"{self.canny_high_var.get():.2f}")
        self.euler_threshold_label.config(text=f"{self.euler_threshold_var.get():.2f}")

    def update_3d_labels(self, event=None):
        """Update 3D parameter labels"""
        if hasattr(self, 'threshold_3d_label'):
            self.threshold_3d_label.config(text=f"{self.threshold_3d_var.get():.2f}")
        if hasattr(self, 'window_size_label'):
            self.window_size_label.config(text=str(int(self.window_size_var.get())))
        if hasattr(self, 'energy_step_label'):
            self.energy_step_label.config(text=f"{self.energy_step_var.get():.3f}")
        if hasattr(self, 'grid_size_label'):
            self.grid_size_label.config(text=str(int(self.grid_size_var.get())))
        if hasattr(self, 'smoothing_3d_label'):
            self.smoothing_3d_label.config(text=f"{self.smoothing_3d_var.get():.2f}")
        if hasattr(self, 'surface_threshold_label'):
            self.surface_threshold_label.config(text=f"{self.surface_threshold_var.get():.2f}")
        if hasattr(self, 'kx_offset_label'):
            self.kx_offset_label.config(text=f"{self.kx_offset_var.get():.2f}")
        if hasattr(self, 'ky_offset_label'):
            self.ky_offset_label.config(text=f"{self.ky_offset_var.get():.2f}")
        if hasattr(self, 'energy_offset_label'):
            self.energy_offset_label.config(text=f"{self.energy_offset_var.get():.2f}")
        if hasattr(self, 'energy_min_label'):
            self.energy_min_label.config(text=f"{self.energy_min_var.get():.2f}")
        if hasattr(self, 'energy_max_label'):
            self.energy_max_label.config(text=f"{self.energy_max_var.get():.2f}")
        if hasattr(self, 'kx_min_label'):
            self.kx_min_label.config(text=f"{self.kx_min_var.get():.2f}")
        if hasattr(self, 'kx_max_label'):
            self.kx_max_label.config(text=f"{self.kx_max_var.get():.2f}")
        if hasattr(self, 'ky_min_label'):
            self.ky_min_label.config(text=f"{self.ky_min_var.get():.2f}")
        if hasattr(self, 'ky_max_label'):
            self.ky_max_label.config(text=f"{self.ky_max_var.get():.2f}")
        if hasattr(self, 'yellow_plane_energy_label'):
            self.yellow_plane_energy_label.config(text=f"{self.yellow_plane_energy_var.get():.2f}")
        if hasattr(self, 'red_plane_energy_label'):
            self.red_plane_energy_label.config(text=f"{self.red_plane_energy_var.get():.2f}")

    def on_setting_change(self, event=None):
        """Handle any setting change"""
        self.update_labels()
        # Update settings dictionary
        self.settings.update({
            'mode': self.mode_var.get(),
            'scan_number': int(self.scan_var.get()),
            'E_binding': self.energy_var.get(),
            'vmin': self.vmin_var.get(),
            'vmax': self.vmax_var.get(),
            'kernel_size': int(self.kernel_var.get()),
            'x_offset': self.x_offset_var.get(),
            'y_offset': self.y_offset_var.get(),
            'colorscale': self.colorscale_var.get(),
            'show_peaks': self.show_peaks_var.get(),
            'use_contours': self.use_contours_var.get(),
            'contour_levels': int(self.contour_var.get()),
            'peak_threshold': self.peak_threshold_var.get(),
            'neighborhood_size': int(self.neighbor_var.get()),
            'smoothing_sigma': self.smooth_var.get(),
            'show_edges': self.show_edges_var.get(),
            'show_components': self.show_components_var.get(),
            'canny_sigma': self.canny_sigma_var.get(),
            'canny_low': self.canny_low_var.get(),
            'canny_high': self.canny_high_var.get(),
            'euler_threshold': self.euler_threshold_var.get(),
            'show_euler_points': self.show_euler_var.get(),
            'show_critical_points': self.show_critical_var.get(),
            'live_update': self.live_update_var.get(),
            'open_in_browser': self.open_in_browser_var.get(),
            'use_plot_window': self.use_plot_window_var.get()
        })

        # Auto-generate plot if live update is enabled and data is loaded
        # Add a small delay to prevent too many rapid updates
        if self.settings['live_update'] and self.data_loader.data_proc:
            # Cancel any pending update
            if hasattr(self, '_update_timer'):
                self.root.after_cancel(self._update_timer)
            # Schedule update after 500ms delay
            self._update_timer = self.root.after(500, self._delayed_plot_update)

    def _delayed_plot_update(self):
        """Delayed plot update to prevent too many rapid updates"""
        try:
            self.generate_plot()
        except Exception as e:
            self.status_label.config(text=f"❌ Live update error: {str(e)}", foreground="red")

    def load_data(self):
        """Load PXT data files"""
        self.status_label.config(text="Loading data...", foreground="orange")
        self.root.update()

        try:
            success, message = self.data_loader.load_pxt_files()

            if success:
                # Update scan range
                max_scans = len(self.data_loader.data_proc) - 1
                self.scan_scale.config(to=max_scans)

                # Update energy range
                E_min, E_max = self.data_loader.get_binding_energy_range()
                self.energy_scale.config(from_=E_min, to=E_max)
                self.energy_var.set((E_min + E_max) / 2)

                self.status_label.config(text=f"✅ {message}", foreground="green")
            else:
                self.status_label.config(text=f"❌ {message}", foreground="red")

        except Exception as e:
            self.status_label.config(text=f"❌ Error: {str(e)}", foreground="red")

    def generate_plot(self):
        """Generate and display plot"""
        if not self.data_loader.data_proc:
            self.status_label.config(text="❌ No data loaded", foreground="red")
            return

        self.status_label.config(text="Generating plot...", foreground="orange")
        self.root.update()

        try:
            if self.settings['mode'] == 'E vs kx':
                fig, message = self.plotter.plot_E_vs_kx(
                    scan_number=self.settings['scan_number'],
                    vmin=self.settings['vmin'],
                    vmax=self.settings['vmax'],
                    kernel_size=self.settings['kernel_size'],
                    x_offset=self.settings['x_offset'],
                    y_offset=self.settings['y_offset'],
                    colorscale=self.settings['colorscale'],
                    show_peaks=self.settings['show_peaks'],
                    peak_threshold=self.settings['peak_threshold'],
                    neighborhood_size=self.settings['neighborhood_size'],
                    smoothing_sigma=self.settings['smoothing_sigma'],
                    show_edges=self.settings['show_edges'],
                    show_components=self.settings['show_components'],
                    canny_sigma=self.settings['canny_sigma'],
                    canny_low=self.settings['canny_low'],
                    canny_high=self.settings['canny_high'],
                    euler_threshold=self.settings['euler_threshold'],
                    show_euler_points=self.settings['show_euler_points'],
                    show_critical_points=self.settings['show_critical_points']
                )
            elif self.settings['mode'] == 'kx vs ky':
                fig, message = self.plotter.plot_kx_vs_ky(
                    E_binding=self.settings['E_binding'],
                    vmin=self.settings['vmin'],
                    vmax=self.settings['vmax'],
                    kernel_size=self.settings['kernel_size'],
                    x_offset=self.settings['x_offset'],
                    y_offset=self.settings['y_offset'],
                    colorscale=self.settings['colorscale'],
                    use_contours=self.settings['use_contours'],
                    contour_levels=self.settings['contour_levels'],
                    show_peaks=self.settings['show_peaks'],
                    peak_threshold=self.settings['peak_threshold'],
                    neighborhood_size=self.settings['neighborhood_size'],
                    smoothing_sigma=self.settings['smoothing_sigma'],
                    show_edges=self.settings['show_edges'],
                    show_components=self.settings['show_components'],
                    canny_sigma=self.settings['canny_sigma'],
                    canny_low=self.settings['canny_low'],
                    canny_high=self.settings['canny_high'],
                    euler_threshold=self.settings['euler_threshold'],
                    show_euler_points=self.settings['show_euler_points'],
                    show_critical_points=self.settings['show_critical_points']
                )
            elif self.settings['mode'] == 'kx vs kz':
                fig, message = self.plotter.plot_kx_vs_kz(
                    E_binding=self.settings['E_binding'],
                    vmin=self.settings['vmin'],
                    vmax=self.settings['vmax'],
                    kernel_size=self.settings['kernel_size'],
                    x_offset=self.settings['x_offset'],
                    y_offset=self.settings['y_offset'],
                    colorscale=self.settings['colorscale'],
                    use_contours=self.settings['use_contours'],
                    contour_levels=self.settings['contour_levels'],
                    show_peaks=self.settings['show_peaks'],
                    peak_threshold=self.settings['peak_threshold'],
                    neighborhood_size=self.settings['neighborhood_size'],
                    smoothing_sigma=self.settings['smoothing_sigma'],
                    show_edges=self.settings['show_edges'],
                    show_components=self.settings['show_components'],
                    canny_sigma=self.settings['canny_sigma'],
                    canny_low=self.settings['canny_low'],
                    canny_high=self.settings['canny_high'],
                    euler_threshold=self.settings['euler_threshold'],
                    show_euler_points=self.settings['show_euler_points'],
                    show_critical_points=self.settings['show_critical_points']
                )
            else:
                self.status_label.config(text="❌ Plot mode not implemented", foreground="red")
                return

            if fig:
                # Always write to the same file
                fig.write_html(self.plot_file.name)

                # Update plot window if it exists and is enabled
                if self.settings['use_plot_window']:
                    # Auto-open plot window if it's not already open
                    if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                        self.plot_window.show()

                    self.plot_window.update_plot(self.plot_file.name)
                    self.status_label.config(text="✅ Plot generated and updated in plot window", foreground="green")

                # Also open in browser if requested
                if self.settings['open_in_browser']:
                    try:
                        # Try to reuse existing browser window/tab
                        browser = webbrowser.get()
                        browser.open('file://' + self.plot_file.name, new=0)
                    except:
                        webbrowser.open('file://' + self.plot_file.name, new=0)
                    self.status_label.config(text="✅ Plot generated and opened in browser", foreground="green")

                # If neither option is selected, just save the file
                if not self.settings['use_plot_window'] and not self.settings['open_in_browser']:
                    self.status_label.config(text="✅ Plot generated and saved to file", foreground="green")
            else:
                self.status_label.config(text=f"❌ {message}", foreground="red")

        except Exception as e:
            self.status_label.config(text=f"❌ Error: {str(e)}", foreground="red")

    def show_plot_window(self):
        """Show the dedicated plot window"""
        self.plot_window.show()
        if os.path.exists(self.plot_file.name):
            self.plot_window.update_plot(self.plot_file.name)
            self.status_label.config(text="✅ Plot window opened", foreground="green")
        else:
            self.status_label.config(text="📊 Plot window opened. Generate a plot to view it.", foreground="blue")

    def export_plot(self):
        """Export current plot"""
        if not self.plotter.current_fig:
            self.status_label.config(text="❌ No plot to export", foreground="red")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                title="Save plot as..."
            )

            if filename:
                self.plotter.current_fig.write_html(filename)
                self.status_label.config(text=f"✅ Plot exported to {Path(filename).name}", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Export error: {str(e)}", foreground="red")

    def load_processed_data(self):
        """Load previously processed 3D data from .npy file"""
        try:
            filename = filedialog.askopenfilename(
                title="Load Processed Data",
                filetypes=[("NumPy files", "*.npy"), ("All files", "*.*")]
            )

            if filename:
                self.update_progress(20, "Loading processed data...")

                # Load data from numpy file
                loaded_data = np.load(filename)
                plot_data = pd.DataFrame(loaded_data, columns=['binding_energy', 'kx', 'ky', 'intensity'])

                # Set as processed data in analyzer
                self.analyzer_3d.processed_data = plot_data.set_index(['binding_energy', 'kx', 'ky'])

                self.update_progress(100, "Data loaded successfully")
                self.status_label.config(text=f"✅ Loaded processed data: {Path(filename).name}", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error loading data: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def save_processed_data(self):
        """Save processed 3D data to .npy file"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "No processed data to save. Process data first.")
            return

        try:
            filename = filedialog.asksaveasfilename(
                title="Save Processed Data",
                defaultextension=".npy",
                filetypes=[("NumPy files", "*.npy"), ("All files", "*.*")]
            )

            if filename:
                self.update_progress(50, "Saving processed data...")

                # Save data to numpy file
                data_to_save = self.analyzer_3d.processed_data.reset_index().to_numpy()
                np.save(filename, data_to_save)

                self.update_progress(100, "Data saved successfully")
                self.status_label.config(text=f"✅ Saved processed data: {Path(filename).name}", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error saving data: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def process_3d_data(self):
        """Process data for 3D analysis"""
        try:
            self.update_progress(0, "Starting 3D data processing...")

            result, message = self.analyzer_3d.process_threshold_and_filter(
                threshold=self.threshold_3d_var.get(),
                window_size=int(self.window_size_var.get()),
                suppress_at=self.suppress_var.get()
            )

            if result is not None:
                self.status_label.config(text="✅ 3D data processed successfully", foreground="green")
            else:
                self.status_label.config(text=f"❌ {message}", foreground="red")

        except Exception as e:
            self.status_label.config(text=f"❌ Error processing 3D data: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def plot_3d_scatter(self):
        """Create 3D scatter plot"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first.")
            return

        try:
            self.update_progress(50, "Creating 3D scatter plot...")

            # Create 3D scatter plot using plotly
            data = self.analyzer_3d.processed_data.reset_index()
            data = data[data['intensity'] > 0]

            # Downsample for performance
            if len(data) > 10000:
                data = data.sample(n=10000)

            labels = create_latex_labels()
            fig = go.Figure(data=go.Scatter3d(
                x=data['kx'],
                y=data['ky'],
                z=data['binding_energy'],
                mode='markers',
                marker=dict(
                    size=2,
                    color=data['intensity'],
                    colorscale='Viridis',
                    opacity=0.8,
                    colorbar=dict(title=labels['intensity'])
                ),
                hovertemplate='k<sub>x</sub>: %{x:.3f} Å⁻¹<br>' +
                             'k<sub>y</sub>: %{y:.3f} Å⁻¹<br>' +
                             'E<sub>b</sub>: %{z:.3f} eV<br>' +
                             'Intensity: %{marker.color:.3f}<extra></extra>'
            ))

            fig.update_layout(
                title='3D ARPES Data Scatter Plot',
                scene=dict(
                    xaxis_title=labels['kx'],
                    yaxis_title=labels['ky'],
                    zaxis_title=labels['E_binding']
                ),
                width=900,
                height=700
            )

            # Save and display
            fig.write_html(self.plot_file.name)
            self.plot_window.update_plot(self.plot_file.name)
            if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                self.plot_window.show()

            self.update_progress(100, "3D scatter plot created")
            self.status_label.config(text="✅ 3D scatter plot created", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error creating 3D plot: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def plot_3d_surface(self):
        """Create 3D surface plot"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first or load processed data.")
            return

        try:
            self.update_progress(10, "Creating 3D surface plot...")

            # Get parameters from GUI
            energy_step = self.energy_step_var.get()
            grid_size = int(self.grid_size_var.get())
            smoothing_sigma = self.smoothing_3d_var.get()
            surface_threshold = self.surface_threshold_var.get()
            single_color_mode = self.single_color_var.get()

            # Range parameters
            energy_range = (self.energy_min_var.get(), self.energy_max_var.get())
            kx_range = (self.kx_min_var.get(), self.kx_max_var.get())
            ky_range = (self.ky_min_var.get(), self.ky_max_var.get())

            # Offset parameters
            kx_offset = self.kx_offset_var.get()
            ky_offset = self.ky_offset_var.get()
            energy_offset = self.energy_offset_var.get()

            # Plane parameters
            show_yellow_plane = self.show_yellow_plane_var.get()
            yellow_plane_energy = self.yellow_plane_energy_var.get() if show_yellow_plane else None
            show_red_plane = self.show_red_plane_var.get()
            red_plane_energy = self.red_plane_energy_var.get() if show_red_plane else None

            self.update_progress(30, "Processing data for surface...")

            # Get processed data
            df = self.analyzer_3d.processed_data.reset_index()

            # Filter data by ranges
            df_filtered = df[
                (df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1]) &
                (df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1]) &
                (df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1]) &
                (df['intensity'] > 0)
            ]

            if len(df_filtered) == 0:
                self.status_label.config(text="❌ No data in specified ranges", foreground="red")
                return

            self.update_progress(50, "Creating energy surfaces...")

            # Create energy values for surfaces
            energy_values = np.arange(energy_range[0], energy_range[1] + energy_step, energy_step)

            # Create coordinate grids
            kx_grid = np.linspace(kx_range[0], kx_range[1], grid_size)
            ky_grid = np.linspace(ky_range[0], ky_range[1], grid_size)
            KX, KY = np.meshgrid(kx_grid, ky_grid)

            # Create 3D intensity grid
            intensity_grid = np.zeros((len(energy_values), grid_size, grid_size))

            for i, energy in enumerate(energy_values):
                # Get data near this energy
                energy_data = df_filtered[
                    np.abs(df_filtered['binding_energy'] - energy) <= energy_step/2
                ]

                if len(energy_data) > 0:
                    # Interpolate intensity values
                    intensity_interp = griddata(
                        points=(energy_data['kx'].values, energy_data['ky'].values),
                        values=energy_data['intensity'].values,
                        xi=(KX, KY),
                        method='cubic',
                        fill_value=0
                    )
                    intensity_grid[i] = np.nan_to_num(intensity_interp)

            self.update_progress(70, "Applying smoothing...")

            # Apply Gaussian smoothing
            intensity_grid = gaussian_filter(intensity_grid, sigma=smoothing_sigma)

            # Create 3D meshgrids for plotting
            Z = np.repeat(energy_values[:, np.newaxis, np.newaxis], grid_size, axis=1)
            Z = np.repeat(Z, grid_size, axis=2)
            X_3d = np.repeat(KX[np.newaxis, :, :], len(energy_values), axis=0)
            Y_3d = np.repeat(KY[np.newaxis, :, :], len(energy_values), axis=0)

            # Apply offsets for visualization
            X_3d_display = X_3d + kx_offset
            Y_3d_display = Y_3d + ky_offset
            Z_display = Z + energy_offset

            self.update_progress(90, "Creating 3D visualization...")

            # Create figure
            fig = go.Figure()

            # Flatten arrays for isosurface
            x_flat = X_3d_display.flatten()
            y_flat = Y_3d_display.flatten()
            z_flat = Z_display.flatten()
            intensity_flat = intensity_grid.flatten()

            # Filter out zero intensities for better performance
            mask = intensity_flat > surface_threshold
            x_filtered = x_flat[mask]
            y_filtered = y_flat[mask]
            z_filtered = z_flat[mask]
            intensity_filtered = intensity_flat[mask]

            if len(intensity_filtered) > 0:
                if single_color_mode:
                    # Single color surface
                    fig.add_trace(go.Isosurface(
                        x=x_filtered,
                        y=y_filtered,
                        z=z_filtered,
                        value=intensity_filtered,
                        isomin=surface_threshold,
                        isomax=np.max(intensity_filtered) * 0.9,
                        surface_count=3,
                        colorscale='Blues',
                        opacity=0.7,
                        caps=dict(x_show=False, y_show=False, z_show=False),
                        showscale=False
                    ))
                else:
                    # Intensity-colored surface
                    fig.add_trace(go.Isosurface(
                        x=x_filtered,
                        y=y_filtered,
                        z=z_filtered,
                        value=intensity_filtered,
                        isomin=surface_threshold,
                        isomax=np.max(intensity_filtered) * 0.9,
                        surface_count=5,
                        colorscale='Viridis',
                        opacity=0.8,
                        caps=dict(x_show=False, y_show=False, z_show=False),
                        colorbar=dict(title="Intensity")
                    ))

            # Add reference planes if requested
            if show_yellow_plane and yellow_plane_energy is not None:
                self._add_reference_plane(fig, yellow_plane_energy + energy_offset,
                                        kx_range, ky_range, 'yellow', 0.3)

            if show_red_plane and red_plane_energy is not None:
                self._add_reference_plane(fig, red_plane_energy + energy_offset,
                                        kx_range, ky_range, 'red', 0.3)

            # Update layout
            labels = create_latex_labels()
            kx_label = f"kx: [{kx_range[0]:.2f} to {kx_range[1]:.2f}]"
            ky_label = f"ky: [{ky_range[0]:.2f} to {ky_range[1]:.2f}]"
            energy_label = f"E: [{energy_range[0]:.2f} to {energy_range[1]:.2f}]"
            range_title = f"{kx_label}, {ky_label}, {energy_label}"

            fig.update_layout(
                title=f"ARPES Data 3D Surface<br><sub>{range_title}</sub>",
                scene=dict(
                    xaxis_title=labels['kx'],
                    yaxis_title=labels['ky'],
                    zaxis_title=labels['E_binding'],
                    aspectratio=dict(x=1, y=1, z=1),
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    )
                ),
                width=900,
                height=700
            )

            # Save and display
            fig.write_html(self.plot_file.name)
            self.plot_window.update_plot(self.plot_file.name)
            if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                self.plot_window.show()

            self.update_progress(100, "3D surface plot created")
            self.status_label.config(text="✅ 3D surface plot created", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error creating 3D surface: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def _add_reference_plane(self, fig, z_value, kx_range, ky_range, color, opacity):
        """Add a reference plane to the 3D plot"""
        x_plane = [kx_range[0], kx_range[1], kx_range[1], kx_range[0]]
        y_plane = [ky_range[0], ky_range[0], ky_range[1], ky_range[1]]
        z_plane = [z_value] * 4

        fig.add_trace(go.Mesh3d(
            x=x_plane + x_plane,  # Duplicate for both sides
            y=y_plane + y_plane,
            z=z_plane + z_plane,
            i=[0, 0, 0, 1],
            j=[1, 2, 3, 2],
            k=[2, 3, 1, 3],
            color=color,
            opacity=opacity,
            name=f'{color.title()} Plane (E={z_value:.2f})'
        ))

    def plot_projections(self):
        """Create projection plots"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first or load processed data.")
            return

        try:
            self.update_progress(20, "Creating projection plots...")

            # Get processed data
            df = self.analyzer_3d.processed_data.reset_index()
            df_nonzero = df[df['intensity'] > 0]

            if len(df_nonzero) == 0:
                self.status_label.config(text="❌ No non-zero intensity data", foreground="red")
                return

            # Create subplots for projections
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('E vs kx Projection', 'E vs ky Projection',
                               'kx vs ky Projection', '3D Scatter Sample'),
                specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
                       [{"type": "heatmap"}, {"type": "scatter3d"}]]
            )

            self.update_progress(40, "Creating E vs kx projection...")

            # E vs kx projection (sum over ky)
            ekx_proj = df_nonzero.groupby(['binding_energy', 'kx'])['intensity'].sum().reset_index()
            ekx_pivot = ekx_proj.pivot(index='binding_energy', columns='kx', values='intensity').fillna(0)

            fig.add_trace(go.Heatmap(
                x=ekx_pivot.columns,
                y=ekx_pivot.index,
                z=ekx_pivot.values,
                colorscale='Viridis',
                name='E vs kx',
                showscale=False
            ), row=1, col=1)

            self.update_progress(60, "Creating E vs ky projection...")

            # E vs ky projection (sum over kx)
            eky_proj = df_nonzero.groupby(['binding_energy', 'ky'])['intensity'].sum().reset_index()
            eky_pivot = eky_proj.pivot(index='binding_energy', columns='ky', values='intensity').fillna(0)

            fig.add_trace(go.Heatmap(
                x=eky_pivot.columns,
                y=eky_pivot.index,
                z=eky_pivot.values,
                colorscale='Viridis',
                name='E vs ky',
                showscale=False
            ), row=1, col=2)

            self.update_progress(80, "Creating kx vs ky projection...")

            # kx vs ky projection (sum over energy)
            kxky_proj = df_nonzero.groupby(['kx', 'ky'])['intensity'].sum().reset_index()
            kxky_pivot = kxky_proj.pivot(index='ky', columns='kx', values='intensity').fillna(0)

            fig.add_trace(go.Heatmap(
                x=kxky_pivot.columns,
                y=kxky_pivot.index,
                z=kxky_pivot.values,
                colorscale='Viridis',
                name='kx vs ky',
                colorbar=dict(title="Intensity")
            ), row=2, col=1)

            # 3D scatter sample (downsampled)
            sample_data = df_nonzero.sample(n=min(5000, len(df_nonzero)))

            fig.add_trace(go.Scatter3d(
                x=sample_data['kx'],
                y=sample_data['ky'],
                z=sample_data['binding_energy'],
                mode='markers',
                marker=dict(
                    size=2,
                    color=sample_data['intensity'],
                    colorscale='Viridis',
                    opacity=0.6
                ),
                name='3D Sample'
            ), row=2, col=2)

            # Update layout
            labels = create_latex_labels()
            fig.update_layout(
                title="ARPES Data Projections",
                height=800,
                width=1000
            )

            # Update axis labels
            fig.update_xaxes(title_text=labels['kx'], row=1, col=1)
            fig.update_yaxes(title_text=labels['E_binding'], row=1, col=1)
            fig.update_xaxes(title_text=labels['ky'], row=1, col=2)
            fig.update_yaxes(title_text=labels['E_binding'], row=1, col=2)
            fig.update_xaxes(title_text=labels['kx'], row=2, col=1)
            fig.update_yaxes(title_text=labels['ky'], row=2, col=1)

            # Save and display
            fig.write_html(self.plot_file.name)
            self.plot_window.update_plot(self.plot_file.name)
            if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                self.plot_window.show()

            self.update_progress(100, "Projection plots created")
            self.status_label.config(text="✅ Projection plots created", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error creating projections: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def find_critical_points(self):
        """Find and display critical points"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first or load processed data.")
            return

        try:
            self.update_progress(20, "Finding critical points...")

            # Get processed data
            df = self.analyzer_3d.processed_data.reset_index()
            df_nonzero = df[df['intensity'] > 0]

            if len(df_nonzero) == 0:
                self.status_label.config(text="❌ No non-zero intensity data", foreground="red")
                return

            self.update_progress(40, "Analyzing intensity gradients...")

            # Create a regular grid for gradient analysis
            grid_size = 100
            kx_range = (df_nonzero['kx'].min(), df_nonzero['kx'].max())
            ky_range = (df_nonzero['ky'].min(), df_nonzero['ky'].max())
            energy_range = (df_nonzero['binding_energy'].min(), df_nonzero['binding_energy'].max())

            # Sample data at regular energy slices
            energy_slices = np.linspace(energy_range[0], energy_range[1], 20)
            critical_points = []

            for energy in energy_slices:
                # Get data near this energy
                energy_data = df_nonzero[
                    np.abs(df_nonzero['binding_energy'] - energy) <= 0.05
                ]

                if len(energy_data) < 10:
                    continue

                # Create grid for this energy slice
                kx_grid = np.linspace(kx_range[0], kx_range[1], grid_size)
                ky_grid = np.linspace(ky_range[0], ky_range[1], grid_size)
                KX, KY = np.meshgrid(kx_grid, ky_grid)

                # Interpolate intensity
                intensity_grid = griddata(
                    points=(energy_data['kx'].values, energy_data['ky'].values),
                    values=energy_data['intensity'].values,
                    xi=(KX, KY),
                    method='cubic',
                    fill_value=0
                )
                intensity_grid = np.nan_to_num(intensity_grid)

                # Apply smoothing
                intensity_smooth = gaussian_filter(intensity_grid, sigma=2.0)

                # Find critical points using Hessian analysis
                maxima, minima, saddles = self.plotter.find_critical_points(intensity_smooth, sigma=1.0)

                # Convert grid indices to actual coordinates
                for coords in np.argwhere(maxima):
                    i, j = coords
                    if 0 < i < grid_size-1 and 0 < j < grid_size-1:  # Avoid edges
                        critical_points.append({
                            'type': 'maximum',
                            'kx': KX[i, j],
                            'ky': KY[i, j],
                            'energy': energy,
                            'intensity': intensity_smooth[i, j]
                        })

                for coords in np.argwhere(minima):
                    i, j = coords
                    if 0 < i < grid_size-1 and 0 < j < grid_size-1:
                        critical_points.append({
                            'type': 'minimum',
                            'kx': KX[i, j],
                            'ky': KY[i, j],
                            'energy': energy,
                            'intensity': intensity_smooth[i, j]
                        })

                for coords in np.argwhere(saddles):
                    i, j = coords
                    if 0 < i < grid_size-1 and 0 < j < grid_size-1:
                        critical_points.append({
                            'type': 'saddle',
                            'kx': KX[i, j],
                            'ky': KY[i, j],
                            'energy': energy,
                            'intensity': intensity_smooth[i, j]
                        })

            self.update_progress(80, "Creating critical points visualization...")

            if not critical_points:
                self.status_label.config(text="❌ No critical points found", foreground="red")
                return

            # Convert to DataFrame
            cp_df = pd.DataFrame(critical_points)

            # Create 3D scatter plot of critical points
            fig = go.Figure()

            # Plot different types of critical points
            for cp_type in ['maximum', 'minimum', 'saddle']:
                cp_subset = cp_df[cp_df['type'] == cp_type]
                if len(cp_subset) > 0:
                    colors = {'maximum': 'red', 'minimum': 'blue', 'saddle': 'orange'}
                    symbols = {'maximum': 'circle', 'minimum': 'square', 'saddle': 'diamond'}

                    fig.add_trace(go.Scatter3d(
                        x=cp_subset['kx'],
                        y=cp_subset['ky'],
                        z=cp_subset['energy'],
                        mode='markers',
                        marker=dict(
                            size=8,
                            color=colors[cp_type],
                            symbol=symbols[cp_type],
                            opacity=0.8
                        ),
                        name=f'{cp_type.title()} ({len(cp_subset)})',
                        hovertemplate=f'{cp_type.title()}<br>' +
                                     'kx: %{x:.3f} Å⁻¹<br>' +
                                     'ky: %{y:.3f} Å⁻¹<br>' +
                                     'E: %{z:.3f} eV<extra></extra>'
                    ))

            # Update layout
            labels = create_latex_labels()
            fig.update_layout(
                title=f"Critical Points Analysis ({len(critical_points)} points found)",
                scene=dict(
                    xaxis_title=labels['kx'],
                    yaxis_title=labels['ky'],
                    zaxis_title=labels['E_binding'],
                    aspectratio=dict(x=1, y=1, z=1),
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    )
                ),
                width=900,
                height=700
            )

            # Save and display
            fig.write_html(self.plot_file.name)
            self.plot_window.update_plot(self.plot_file.name)
            if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                self.plot_window.show()

            self.update_progress(100, "Critical points analysis complete")
            self.status_label.config(text=f"✅ Found {len(critical_points)} critical points", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error finding critical points: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def run(self):
        """Start the GUI application"""
        try:
            self.root.mainloop()
        finally:
            # Clean up temporary plot file
            try:
                if os.path.exists(self.plot_file.name):
                    os.unlink(self.plot_file.name)
            except:
                pass


def main():
    """Main function to run the application"""
    print("🔬 Starting Enhanced ARPES Analysis GUI...")
    print("✨ Features: Live updates, advanced analysis, multiple plot modes")
    print("📋 Make sure you have the required packages installed:")
    print("   pip install plotly pandas numpy scipy scikit-image")
    print("🌟 New in this version:")
    print("   - Live plot updates when parameters change")
    print("   - Edge detection and component analysis")
    print("   - Euler characteristic calculation")
    print("   - Critical point detection")
    print("   - kx vs kz plotting mode")
    print("   - Enhanced peak detection")
    print()

    try:
        app = ARPESAnalysisGUI()
        app.run()
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        messagebox.showerror("Error", f"Failed to start application:\n{e}")


if __name__ == "__main__":
    main()


import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
import matplotlib.pyplot as plt
from ipywidgets import Output, VBox, Dropdown
from matplotlib.backends.backend_agg import FigureCanvasAgg
from IPython.display import display, clear_output
from ipywidgets import HBox, VBox, HTML

%matplotlib widget
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.main_output = Output()

    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()

        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return

        k_space_module = self.plot_modules["K-Space Plot"](self.data_files, self.work_function)
        edc_module = self.plot_modules["EDC Plot"](self.data_files, self.work_function)

        k_space_interface = k_space_module.run()
        edc_interface = edc_module.run()

        main_interface = HBox([VBox([HTML("<h2>K-Space Plot</h2>"), k_space_interface]),
                               VBox([HTML("<h2>EDC Plot</h2>"), edc_interface])])
        display(main_interface)
class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold,
                moving_average_size, dft_x_offset, dft_y_offset):
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)

    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        self.fig.canvas.draw_idle()



    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()

