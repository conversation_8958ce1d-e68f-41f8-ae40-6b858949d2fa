#!/usr/bin/env python3
"""
Test script for the enhanced ARPES Analysis GUI
This script tests the basic functionality without requiring actual data files.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_imports():
    """Test that all required imports work"""
    print("Testing GUI imports...")
    
    try:
        from arpes_analysis_gui import ARPESAnalysisGUI
        print("✅ Successfully imported ARPESAnalysisGUI")
        return True
    except ImportError as e:
        print(f"❌ Failed to import ARPESAnalysisGUI: {e}")
        return False

def test_gui_initialization():
    """Test GUI initialization"""
    print("Testing GUI initialization...")
    
    try:
        from arpes_analysis_gui import ARPESAnalysisGUI
        
        # Create GUI instance (but don't run it)
        app = ARPESAnalysisGUI()
        print("✅ Successfully initialized GUI")
        
        # Test that all required attributes exist
        required_attrs = [
            'data_loader', 'plotter', 'analyzer_3d', 'plot_window',
            'threshold_3d_var', 'energy_step_var', 'grid_size_var',
            'smoothing_3d_var', 'surface_threshold_var'
        ]
        
        for attr in required_attrs:
            if hasattr(app, attr):
                print(f"✅ Found attribute: {attr}")
            else:
                print(f"❌ Missing attribute: {attr}")
                return False
        
        # Clean up
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize GUI: {e}")
        return False

def test_3d_functionality():
    """Test 3D functionality methods exist"""
    print("Testing 3D functionality...")
    
    try:
        from arpes_analysis_gui import ARPESAnalysisGUI
        
        app = ARPESAnalysisGUI()
        
        # Test that 3D methods exist
        required_methods = [
            'load_processed_data', 'save_processed_data', 'process_3d_data',
            'plot_3d_scatter', 'plot_3d_surface', 'plot_projections',
            'find_critical_points', 'update_3d_labels'
        ]
        
        for method in required_methods:
            if hasattr(app, method) and callable(getattr(app, method)):
                print(f"✅ Found method: {method}")
            else:
                print(f"❌ Missing method: {method}")
                app.root.destroy()
                return False
        
        # Clean up
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Failed to test 3D functionality: {e}")
        return False

def main():
    """Run all tests"""
    print("🔬 Testing Enhanced ARPES Analysis GUI")
    print("=" * 50)
    
    tests = [
        test_gui_imports,
        test_gui_initialization,
        test_3d_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The GUI should work correctly.")
        print("\n🚀 To run the GUI, execute:")
        print("   python arpes_analysis_gui.py")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
