#!/usr/bin/env python3
"""
Launcher for the ARPES Analysis GUI - pyarpesenv Environment
This script checks the environment and launches the main application.
"""

import sys
import subprocess
import importlib
import os

def check_conda_env():
    """Check if we're in the pyarpesenv conda environment"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', '')
    if conda_env == 'pyarpesenv':
        return True

    # Also check CONDA_PREFIX
    conda_prefix = os.environ.get('CONDA_PREFIX', '')
    if 'pyarpesenv' in conda_prefix:
        return True

    return False

def check_package(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name

    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package_conda(package_name):
    """Install a package using conda"""
    try:
        subprocess.check_call(["conda", "install", "-y", package_name])
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_package_pip(package_name):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Main launcher function"""
    print("🔬 ARPES Analysis GUI Launcher")
    print("=" * 40)

    # Check conda environment
    print("🐍 Checking conda environment...")
    if check_conda_env():
        print("✅ Running in pyarpesenv environment")
    else:
        print("⚠️  Not in pyarpesenv environment")
        print("   Current environment:", os.environ.get('CONDA_DEFAULT_ENV', 'base'))
        print("   Please activate pyarpesenv:")
        print("   conda activate pyarpesenv")
        print("   Then run this script again.")

        response = input("\nContinue anyway? (y/N): ").lower().strip()
        if response != 'y':
            return False

    # Required packages with conda/pip preferences
    required_packages = [
        ("plotly", "plotly", "pip"),  # Usually better from pip
        ("pandas", "pandas", "conda"),
        ("numpy", "numpy", "conda"),
        ("scipy", "scipy", "conda"),
        ("scikit-image", "skimage", "conda"),
        ("xarray", "xarray", "conda")
    ]

    missing_packages = []

    # Check for missing packages
    print("\n📋 Checking dependencies...")
    for package_name, import_name, _ in required_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name}")
        else:
            print(f"❌ {package_name} - MISSING")
            missing_packages.append((package_name, import_name, _))

    # Check for ARPES package specifically
    print("📋 Checking ARPES package...")
    if check_package("arpes", "arpes.load_pxt"):
        print("✅ arpes.load_pxt")
    else:
        print("❌ arpes.load_pxt - MISSING")
        print("   This is required for loading PXT files")
        print("   Make sure you're in the pyarpesenv environment")

    # Install missing packages
    if missing_packages:
        print(f"\n📦 Installing {len(missing_packages)} missing packages...")
        for package_name, import_name, preferred_method in missing_packages:
            print(f"Installing {package_name}...")

            success = False
            if preferred_method == "conda":
                success = install_package_conda(package_name)
                if not success:
                    print(f"   Conda install failed, trying pip...")
                    success = install_package_pip(package_name)
            else:
                success = install_package_pip(package_name)
                if not success:
                    print(f"   Pip install failed, trying conda...")
                    success = install_package_conda(package_name)

            if success:
                print(f"✅ {package_name} installed successfully")
            else:
                print(f"❌ Failed to install {package_name}")
                print("Please install manually with:")
                print(f"   conda install {package_name}")
                print(f"   # or: pip install {package_name}")
                return False

    print("\n🚀 All dependencies satisfied!")
    print("🔬 Launching ARPES Analysis GUI...")

    # Import and run the main application
    try:
        from arpes_analysis_gui import main as run_gui
        run_gui()
    except ImportError as e:
        print(f"❌ Error importing GUI: {e}")
        print("Make sure arpes_analysis_gui.py is in the same directory")
        return False
    except Exception as e:
        print(f"❌ Error running GUI: {e}")
        return False

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
        sys.exit(1)
