import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

igor_data = np.array([
    [57600, 54784, 58112],
    [56561.95, 53415.66, 57121.13],
    [55523.89, 52047.31, 56130.26],
    [54485.84, 50678.96, 55139.39],
    [53447.78, 49310.62, 54148.52],
    [52409.73, 47942.27, 53157.65],
    [51371.67, 46655.25, 52193.88],
    [50333.62, 45428.45, 51250.2],
    [49295.56, 44201.66, 50306.51],
    [48257.51, 42974.87, 49362.82],
    [47219.45, 41748.08, 48419.14],
    [46223.56, 40563.45, 47510.59],
    [45468.61, 39619.77, 46802.82],
    [44713.66, 38676.08, 46095.06],
    [43958.71, 37732.39, 45387.29],
    [43203.77, 36788.71, 44679.53],
    [42448.82, 35845.02, 43971.77],
    [41693.87, 34833.07, 43195.73],
    [40938.92, 33795.01, 42393.6],
    [40183.97, 32756.96, 41591.46],
    [39429.02, 31718.9, 40789.33],
    [38674.07, 30680.85, 39987.2],
    [37919.12, 29670.9, 39171.01],
    [37164.17, 28727.21, 38321.7],
    [36409.22, 27783.53, 37472.38],
    [35654.27, 26839.84, 36623.06],
    [34899.32, 25896.16, 35773.74],
    [34144.38, 24952.47, 34924.42],
    [33512.91, 24173.43, 34239.75],
    [32899.52, 23418.48, 33579.17],
    [32286.12, 22663.53, 32918.59],
    [31672.72, 21908.58, 32258.01],
    [31059.33, 21153.63, 31597.43],
    [30467.01, 20419.77, 30957.93],
    [29900.8, 19712, 30344.53],
    [29334.59, 19004.23, 29731.14],
    [28768.38, 18296.47, 29117.74],
    [28202.16, 17588.71, 28504.35],
    [27641.98, 16886.96, 27901.99],
    [27358.87, 16462.31, 27807.62],
    [27075.77, 16037.65, 27713.26],
    [26792.66, 15612.99, 27618.89],
    [26509.55, 15188.33, 27524.52],
    [26226.45, 14763.67, 27430.15],
    [26027.67, 14479.56, 27448.22],
    [25886.12, 14290.82, 27542.59],
    [25744.56, 14102.09, 27636.96],
    [25603.01, 13913.35, 27731.33],
    [25461.46, 13724.61, 27825.69],
    [25279.75, 13503.75, 27944.16],
    [24902.28, 13126.27, 28180.08],
    [24524.8, 12748.8, 28416],
    [24147.33, 12371.33, 28651.92],
    [23769.85, 11993.85, 28887.84],
    [23392.38, 11616.38, 29123.77],
    [22874.35, 11168.63, 29359.69],
    [22308.14, 10696.78, 29595.61],
    [21741.93, 10224.94, 29831.53],
    [21175.72, 9753.098, 30067.45],
    [20609.51, 9281.255, 30303.37],
    [19952.94, 8899.765, 30539.29],
    [19103.62, 8711.027, 30775.21],
    [18254.31, 8522.29, 31011.14],
    [17404.99, 8333.553, 31247.06],
    [16555.67, 8144.816, 31482.98],
    [15706.35, 7956.079, 31718.9],
    [14688.38, 7893.835, 31828.33],
    [13650.32, 7846.651, 31922.7],
    [12612.27, 7799.467, 32017.07],
    [11574.21, 7752.282, 32111.44],
    [10536.16, 7705.098, 32205.8],
    [9807.31, 7922.949, 32388.52],
    [9429.835, 8441.977, 32671.62],
    [9052.36, 8961.004, 32954.73],
    [8674.887, 9480.031, 33237.84],
    [8297.412, 9999.059, 33520.94],
    [7911.906, 10526.12, 33812.08],
    [7345.694, 11233.88, 34283.92],
    [6779.482, 11941.65, 34755.77],
    [6213.271, 12649.41, 35227.61],
    [5647.059, 13357.18, 35699.45],
    [5080.847, 14064.94, 36171.29],
    [4543.749, 14714.48, 36614.02],
    [4024.722, 15327.87, 37038.68],
    [3505.694, 15941.27, 37463.34],
    [2986.667, 16554.67, 37888],
    [2467.639, 17168.06, 38312.66],
    [1984.753, 17790.49, 38764.42],
    [1654.463, 18451.07, 39330.64],
    [1324.173, 19111.65, 39896.85],
    [993.8823, 19772.23, 40463.06],
    [663.5922, 20432.82, 41029.27],
    [333.302, 21093.4, 41595.48],
    [256, 21464.85, 41944.85],
    [256, 21747.95, 42227.95],
    [256, 22031.06, 42511.06],
    [256, 22314.16, 42794.16],
    [256, 22597.27, 43077.27],
    [239.9373, 23008.88, 43456.75],
    [192.7529, 23669.46, 44022.96],
    [145.5686, 24330.04, 44589.18],
    [98.38432, 24990.62, 45155.39],
    [51.2, 25651.2, 45721.6],
    [4.015687, 26311.78, 46287.81],
    [0, 26972.36, 46897.19],
    [0, 27632.94, 47510.59],
    [0, 28293.52, 48123.98],
    [0, 28954.1, 48737.38],
    [0, 29614.68, 49350.78],
    [0, 30344.53, 50033.44],
    [0, 31146.67, 50788.39],
    [0, 31948.8, 51543.34],
    [0, 32750.93, 52298.29],
    [0, 33553.07, 53053.24],
    [0, 34358.21, 53805.18],
    [0, 35207.53, 54512.94],
    [0, 36056.85, 55220.71],
    [0, 36906.16, 55928.47],
    [0, 37755.48, 56636.23],
    [0, 38604.8, 57344],
    [0, 39062.59, 57208.47],
    [0, 39298.51, 56595.07],
    [0, 39534.43, 55981.68],
    [0, 39770.35, 55368.28],
    [0, 40006.27, 54754.89],
    [0, 40181.96, 54041.1],
    [0, 40134.78, 52955.86],
    [0, 40087.59, 51870.62],
    [0, 40040.41, 50785.38],
    [0, 39993.22, 49700.14],
    [0, 39946.04, 48614.9],
    [0, 39936, 47641.1],
    [0, 39936, 46697.41],
    [0, 39936, 45753.73],
    [0, 39936, 44810.04],
    [0, 39936, 43866.35],
    [0, 39918.93, 42854.4],
    [0, 39871.75, 41721.98],
    [0, 39824.57, 40589.55],
    [0, 39777.38, 39457.13],
    [0, 39730.2, 38324.71],
    [0, 39683.01, 37192.28],
    [0, 39680, 36369.07],
    [0, 39680, 35566.93],
    [0, 39680, 34764.8],
    [0, 39680, 33962.67],
    [0, 39680, 33160.54],
    [0, 39680, 32527.06],
    [0, 39680, 32055.21],
    [0, 39680, 31583.37],
    [0, 39680, 31111.53],
    [0, 39680, 30639.69],
    [0, 39675.98, 30123.67],
    [0, 39628.8, 29132.8],
    [0, 39581.62, 28141.93],
    [0, 39534.43, 27151.06],
    [0, 39487.25, 26160.19],
    [0, 39440.06, 25169.32],
    [0, 39361.76, 24240.69],
    [0, 39267.39, 23344.19],
    [0, 39173.02, 22447.69],
    [0, 39078.65, 21551.18],
    [0, 38984.28, 20654.68],
    [0, 38923.04, 19835.48],
    [0, 38970.23, 19269.27],
    [0, 39017.41, 18703.06],
    [0, 39064.6, 18136.85],
    [0, 39111.78, 17570.63],
    [0, 39158.96, 17004.42],
    [0, 39435.04, 16781.55],
    [0, 39765.33, 16640],
    [0, 40095.62, 16498.45],
    [0, 40425.91, 16356.89],
    [0, 40756.2, 16215.34],
    [993.8823, 41122.64, 16073.79],
    [3589.02, 41547.29, 15932.24],
    [6184.157, 41971.95, 15790.68],
    [8779.294, 42396.61, 15649.13],
    [11374.43, 42821.27, 15507.58],
    [13969.57, 43245.93, 15366.02],
    [15796.71, 43715.77, 15224.47],
    [17589.71, 44187.61, 15082.92],
    [19382.71, 44659.45, 14941.36],
    [21175.72, 45131.29, 14799.81],
    [22968.72, 45603.14, 14658.26],
    [24686.43, 46100.08, 14516.71],
    [26337.88, 46619.11, 14375.15],
    [27989.33, 47138.13, 14233.6],
    [29640.79, 47657.16, 14092.05],
    [31292.23, 48176.19, 13950.49],
    [32933.65, 48705.25, 13798.9],
    [34490.73, 49318.65, 13562.98],
    [36047.81, 49932.05, 13327.06],
    [37604.89, 50545.44, 13091.14],
    [39161.98, 51158.84, 12855.22],
    [40719.06, 51772.23, 12619.29],
    [41922.76, 52225, 12415.5],
    [42960.82, 52602.48, 12226.76],
    [43998.87, 52979.95, 12038.02],
    [45036.93, 53357.43, 11849.29],
    [46074.98, 53734.9, 11660.55],
    [47293.74, 54196.71, 11411.58],
    [49039.56, 54904.47, 10986.92],
    [50785.38, 55612.23, 10562.26],
    [52531.2, 56320, 10137.6],
    [54277.02, 57027.77, 9712.941],
    [56022.84, 57735.53, 9288.282],
    [57494.59, 58325.84, 8785.317],
    [58910.12, 58892.05, 8266.29],
    [60325.65, 59458.26, 7747.263],
    [61741.18, 60024.47, 7228.235],
    [63156.71, 60590.68, 6709.208],
    [64076.3, 60470.21, 6457.224],
    [64265.04, 59337.79, 6598.776],
    [64453.77, 58205.36, 6740.33],
    [64642.51, 57072.94, 6881.882],
    [64831.25, 55940.52, 7023.435],
    [65019.98, 54808.09, 7164.988],
    [64746.92, 53260.05, 7398.902],
    [64463.81, 51702.96, 7634.824],
    [64180.71, 50145.88, 7870.745],
    [63897.6, 48588.8, 8106.667],
    [63614.49, 47031.72, 8342.588],
    [63592.41, 45605.14, 8474.102],
    [63781.14, 44283.98, 8521.286],
    [63969.88, 42962.82, 8568.471],
    [64158.62, 41641.66, 8615.655],
    [64347.36, 40320.5, 8662.839],
    [64415.62, 38993.32, 8704],
    [63660.68, 37624.97, 8704],
    [62905.73, 36256.63, 8704],
    [62150.78, 34888.28, 8704],
    [61395.83, 33519.94, 8704],
    [60640.88, 32151.59, 8704],
    [60283.48, 30882.63, 8704],
    [60094.75, 29655.84, 8704],
    [59906.01, 28429.05, 8704],
    [59717.27, 27202.26, 8704],
    [59528.54, 25975.47, 8704],
    [59339.8, 24722.57, 8704],
    [59151.06, 23401.41, 8704],
    [58962.32, 22080.25, 8704],
    [58773.59, 20759.09, 8704],
    [58584.85, 19437.93, 8704],
    [58396.11, 18116.77, 8704],
    [58287.69, 17197.18, 8704],
    [58193.32, 16347.86, 8704],
    [58098.95, 15498.54, 8704],
    [58004.58, 14649.22, 8704],
    [57910.21, 13799.91, 8704],
    [57795.77, 12267.92, 8704],
    [57654.21, 9814.337, 8704],
    [57512.66, 7360.753, 8704],
    [57371.11, 4907.168, 8704],
    [57229.55, 2453.584, 8704],
    [57088, 0, 8704]])
# Create the array from the provided data


# Normalize the RGB values to the range 0-1
normalized_data = igor_data / 65535.0

# Create the ListedColormap
rainbowlightct = ListedColormap(normalized_data)
rainbowlightct = plt.cm.viridis  # Use built-in colormap


warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128



import os
import pandas as pd
import numpy as np
from arpes.load_pxt import read_single_pxt
import tkinter as tk
from tkinter import filedialog, messagebox

def load_pxt_files():
    folder_path = filedialog.askdirectory(title="Select folder containing PXT files")
    if not folder_path:
        return

    pxt_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    if not pxt_files:
        messagebox.showinfo("No PXT files", "No PXT files found in the selected folder.")
        return

    # Sort the files based on their names
    pxt_files.sort()

    data_arrays = []
    attributes = []
    for file in pxt_files:
        file_path = os.path.join(folder_path, file)
        try:
            data = read_single_pxt(file_path)
            df = pd.DataFrame(data.values, columns=data.coords['phi'].values, index=data.coords['eV'].values)
            data_arrays.append(df)
            attributes.append(data.attrs)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {file}: {str(e)}")

    messagebox.showinfo("Success", f"Loaded {len(data_arrays)} PXT files into pandas DataFrames.")
    return data_arrays, attributes

# Create the main window
root = tk.Tk()
root.title("ARPES PXT File Loader")

# Create and pack a button to trigger file loading
def load_and_store_data():
    global data, data_attributes
    data, data_attributes = load_pxt_files()
    
    # Add new code to correspond data_attributes[i]['polar'] to data[i]
    for i in range(len(data)):
        data[i].attrs['polar'] = data_attributes[i]['polar']

load_button = tk.Button(root, text="Load PXT Files", command=load_and_store_data)
load_button.pack(pady=20)

# Start the GUI event loop
root.mainloop()


work_function = 4.5 #eV
E_photon = np.zeros(np.shape(data_attributes)[0])
for i in range(len(E_photon)):
    E_photon[i]=data_attributes[i]['hv']

E_photon

theta =  np.zeros(np.shape(data_attributes)[0])
for i in range(len(theta)):
    theta[i]=data_attributes[i]['polar']

theta
data_proc = data.copy()
for i in range(len(data_proc)):
    new_index = [data_attributes[i]['hv'] - work_function - abs(idx) for idx in data[i].index]
    data_proc[i] = data_proc[i].set_index(pd.Index(new_index))
data_proc

import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata, LinearNDInterpolator
import ipywidgets as widgets
from IPython.display import display
from matplotlib.colors import ListedColormap
from scipy.ndimage import gaussian_filter, maximum_filter
from skimage.measure import label, euler_number
from skimage.feature import canny
from mpl_toolkits.mplot3d import Axes3D  # Import for 3D plotting
%matplotlib widget
# Normalize the RGB values to the range 0-1
normalized_data = igor_data / 65535.0

# Create the ListedColormap
rainbowlightct = ListedColormap(normalized_data)

# Constants
work_function = 4.5  # eV
V0 = 10  # Inner potential in eV, adjust based on your material properties

# Function to apply moving average filter
def moving_average(data, kernel_size):
    kernel = np.ones(kernel_size) / kernel_size
    return np.convolve(data, kernel, mode='same')

# Compute the binding energy range spanned by the data
E_binding_values = []

for i in range(len(data_proc)):
    df = data_proc[i]
    hv = data_attributes[i]['hv']
    E_kinetic_values = df.index.values.astype(float)
    # Compute binding energies for these kinetic energies
    E_binding_scan = hv - work_function - E_kinetic_values
    E_binding_values.extend(E_binding_scan)

E_binding_array = np.array(E_binding_values)
E_binding_min = E_binding_array.min()
E_binding_max = E_binding_array.max()

# Initialize figure and axes outside the function
fig, ax = plt.subplots(figsize=(10, 8))
pcm = None
cs = None
colorbar = None  # To manage the colorbar

# Function to find peaks in the intensity data
def find_peaks_in_intensity(intensities, neighborhood_size, threshold, smoothing_sigma):
    # Smooth the intensities to reduce noise
    intensities_smooth = gaussian_filter(intensities, sigma=smoothing_sigma)

    # Apply maximum filter to find local maxima
    local_max = maximum_filter(intensities_smooth, size=neighborhood_size) == intensities_smooth

    # Apply threshold to identify significant peaks
    detected_peaks = (intensities_smooth > threshold) & local_max

    # Get peak indices
    peak_indices = np.argwhere(detected_peaks)

    return peak_indices

# Function to generate the constant energy map and identify peaks
def plot_constant_energy_map(
    mode, E_binding, vmin, vmax, use_contours, contour_levels,
    kernel_size, x_offset, y_offset, sigma, low_threshold, high_threshold,
    display_edges, display_components, scan_number, peak_threshold, neighborhood_size, smoothing_sigma,
    euler_binary_threshold, display_euler_points, intensity_threshold, surface_plot, surface_resolution
):
    global pcm, cs, colorbar, fig, ax  # Declare global variables to keep track of plot elements

    # Clear the current figure
    plt.clf()

    if mode == 'E vs kx':
        fig, ax = plt.subplots(figsize=(10, 8))
        colorbar = None  # Reset colorbar

        # Ensure the scan_number is within valid range
        if scan_number < 0 or scan_number >= len(data_proc):
            print("Invalid scan number selected.")
            return

        # Use data from the selected scan only
        df = data_proc[scan_number]
        hv = data_attributes[scan_number]['hv']

        # Emission angles (theta) in degrees, convert to radians
        emission_angles = df.columns.values.astype(float)
        theta_rad = np.deg2rad(emission_angles)

        # Kinetic energy values
        E_kinetic_values = df.index.values.astype(float)

        # Reverse E_kinetic_values to ensure increasing order if necessary
        if np.any(np.diff(E_kinetic_values) < 0):
            E_kinetic_values = E_kinetic_values[::-1]
            df = df.iloc[::-1]

        # Create grids for E_kinetic and theta using 'xy' indexing
        theta_grid, E_kinetic_grid = np.meshgrid(theta_rad, E_kinetic_values, indexing='xy')

        # Compute E_binding_grid
        E_binding_grid = hv - work_function - E_kinetic_grid + y_offset

        # Compute kx for all combinations
        kx_grid = 0.5123 * np.sqrt(E_kinetic_grid) * np.sin(theta_grid) + x_offset

        # Intensities from the data
        intensities = df.values

        # Apply moving average filter along the energy axis
        if kernel_size > 1:
            intensities = np.apply_along_axis(
                lambda m: moving_average(m, kernel_size),
                axis=0,
                arr=intensities
            )

        # Normalize the intensities
        max_intensity = np.nanmax(intensities)
        if max_intensity > 0:
            intensities = intensities / max_intensity

        # Mask invalid data (e.g., due to sqrt of negative energies)
        valid_mask = np.isfinite(kx_grid) & np.isfinite(E_binding_grid) & np.isfinite(intensities)
        intensities[~valid_mask] = np.nan

        # Update the plot
        pcm = ax.pcolormesh(
            kx_grid, E_binding_grid, intensities,
            shading='auto', cmap=rainbowlightct, vmin=vmin, vmax=vmax
        )

        if colorbar is None:
            colorbar = fig.colorbar(pcm, ax=ax, label='Normalized Intensity')
        else:
            colorbar.update_normal(pcm)

        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
        ax.set_ylabel('Binding Energy (eV)')
        ax.set_title(f'Binding Energy vs $k_x$ Map\nScan Number: {scan_number}')

        # Ensure y-axis is inverted (binding energy increases downward)
        ax.set_ylim(ax.get_ylim()[::-1])

        # Prepare intensities for peak detection
        intensities_filled = np.nan_to_num(intensities, nan=0.0)

        # Find peaks in the intensity data
        peak_indices = find_peaks_in_intensity(
            intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
        )

        # Plot peaks
        if peak_indices.size > 0:
            peak_coords = np.array([(kx_grid[i, j], E_binding_grid[i, j]) for i, j in peak_indices])
            ax.plot(peak_coords[:, 0], peak_coords[:, 1], 'o', color='red', label='Peaks')

        # Threshold the intensities to create a binary image for Euler characteristic calculation
        binary_intensity = intensities_filled >= euler_binary_threshold

        # Compute the Euler characteristic
        euler_char = euler_number(binary_intensity)

        # Display the Euler characteristic on the plot
        ax.text(
            0.95, 0.95,
            f'Euler characteristic: {euler_char}',
            transform=ax.transAxes,
            fontsize=12,
            verticalalignment='top',
            horizontalalignment='right',
            bbox=dict(facecolor='white', alpha=0.5)
        )

        # Display binary points used in Euler characteristic calculation
        if display_euler_points:
            # Overlay the binary image onto the plot
            ax.contour(
                kx_grid, E_binding_grid, binary_intensity,
                levels=[0.5], colors='white', linewidths=1.0, linestyles='--', label='Euler Binary'
            )

        # Add legend for peaks and Euler binary contour
        ax.legend(loc='lower left')

        if display_edges or display_components:
            # Apply Canny edge detection
            edges = canny(
                intensities_filled,
                sigma=sigma,
                low_threshold=low_threshold,
                high_threshold=high_threshold
            )

            if display_edges:
                # Overlay edges on the plot
                ax.contour(
                    kx_grid, E_binding_grid, edges,
                    levels=[0.5], colors='cyan', linewidths=1.0
                )

            if display_components:
                # Label connected components
                labeled_array, num_features = label(edges, connectivity=2, return_num=True)

                # Iterate over each detected component
                for region_label in range(1, num_features + 1):
                    # Create a mask for the current component
                    component_mask = labeled_array == region_label

                    # Extract the coordinates of the component pixels
                    kx_component = kx_grid[component_mask]
                    E_binding_component = E_binding_grid[component_mask]

                    # Plot the component
                    ax.plot(
                        kx_component,
                        E_binding_component,
                        '.', markersize=1, color='yellow'
                    )

        plt.tight_layout()
        plt.show()

    elif mode in ['kx vs ky', 'kx vs kz']:
        fig, ax = plt.subplots(figsize=(10, 8))
        colorbar = None  # Reset colorbar

        # Prepare lists to store kx, ky_kz, and intensity values
        kx_list = []
        ky_kz_list = []
        intensity_list = []

        # Loop over all scans
        for i in range(len(data_proc)):
            df = data_proc[i]
            hv = data_attributes[i]['hv']

            # Emission angles (theta) in degrees, convert to radians
            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)

            # Compute the kinetic energy corresponding to the desired binding energy
            E_kinetic = hv - work_function - E_binding

            # Check if E_kinetic is within the kinetic energy range of the data
            E_kinetic_values = df.index.values.astype(float)
            if E_kinetic < E_kinetic_values.min() or E_kinetic > E_kinetic_values.max():
                continue  # Skip if E_kinetic is outside the data range

            # Calculate k_magnitude (wave vector magnitude) in Å⁻¹
            k_magnitude = 0.5123 * np.sqrt(E_kinetic)  # Å⁻¹

            # Calculate kx components
            kx = k_magnitude * np.sin(theta_rad) + x_offset  # Array of kx values

            # Extract intensities at the specified kinetic energy
            if E_kinetic in E_kinetic_values:
                intensities = df.loc[E_kinetic].values
            else:
                # Interpolate intensities at E_kinetic for each theta
                intensities = df.apply(
                    lambda col: np.interp(E_kinetic, E_kinetic_values, col.values)
                ).values

            # Apply moving average filter to intensities
            if kernel_size > 1:
                intensities = moving_average(intensities, kernel_size)

            # Depending on the mode, calculate ky or kz
            if mode == 'kx vs ky':
                # Polar angle for this scan (in degrees)
                polar_angle = data_attributes[i]['polar']
                polar_angle_rad = np.deg2rad(polar_angle)

                # Calculate ky components
                ky = k_magnitude * np.sin(polar_angle_rad) + y_offset  # Scalar ky value

                # Create an array of ky values matching the length of kx
                ky_kz_array = np.full_like(kx, ky)
                ylabel = r'$k_y$ (Å$^{-1}$)'
                title = f'Constant Energy Map at Binding Energy = {E_binding:.2f} eV\nMode: kx vs ky'

            elif mode == 'kx vs kz':
                # Calculate kz components using the inner potential V0
                kz = 0.5123 * np.sqrt(E_kinetic * np.cos(theta_rad)**2 + V0) + y_offset  # Å⁻¹

                ky_kz_array = kz
                ylabel = r'$k_z$ (Å$^{-1}$)'
                title = f'Constant Energy Map at Binding Energy = {E_binding:.2f} eV\nMode: kx vs kz'

            else:
                print("Invalid mode selected.")
                return

            # Append to the lists
            kx_list.extend(kx)
            ky_kz_list.extend(ky_kz_array)
            intensity_list.extend(intensities)

        # Convert lists to numpy arrays
        kx_array = np.array(kx_list)
        ky_kz_array = np.array(ky_kz_list)
        intensity_array = np.array(intensity_list)

        # Check if there is data to plot
        if kx_array.size == 0 or ky_kz_array.size == 0 or intensity_array.size == 0:
            print("No data available for the selected binding energy.")
            return

        # Create grid for interpolation
        grid_resolution = 600
        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)
        ky_kz_grid = np.linspace(ky_kz_array.min(), ky_kz_array.max(), grid_resolution)
        kx_mesh, ky_kz_mesh = np.meshgrid(kx_grid, ky_kz_grid)

        # Interpolate intensity data onto the grid
        intensity_grid = griddata(
            points=(kx_array, ky_kz_array),
            values=intensity_array,
            xi=(kx_mesh, ky_kz_mesh),
            method='cubic'
        )

        # Handle NaN values in the interpolated data
        intensity_grid = np.nan_to_num(intensity_grid)

        # Normalize intensity_grid to maximum value
        max_intensity = intensity_grid.max()
        if max_intensity > 0:
            intensity_grid /= max_intensity

        # Update the plot
        if use_contours:
            # Use the contour_levels parameter to adjust contour density
            cs = ax.contour(
                kx_mesh, ky_kz_mesh, intensity_grid,
                levels=contour_levels, cmap=rainbowlightct
            )
            if colorbar is None:
                colorbar = fig.colorbar(cs, ax=ax, label='Intensity')
            else:
                colorbar.update_normal(cs)
        else:
            pcm = ax.pcolormesh(
                kx_mesh, ky_kz_mesh, intensity_grid,
                shading='auto', cmap=rainbowlightct, vmin=vmin, vmax=vmax
            )
            if colorbar is None:
                colorbar = fig.colorbar(pcm, ax=ax, label='Intensity')
            else:
                colorbar.update_normal(pcm)

        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
        ax.set_ylabel(ylabel)
        ax.set_title(title)

        # Prepare intensities for peak detection
        intensities_filled = np.nan_to_num(intensity_grid, nan=0.0)

        # Find peaks in the intensity data
        peak_indices = find_peaks_in_intensity(
            intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
        )

        # Plot peaks
        if peak_indices.size > 0:
            peak_coords = np.array([(kx_mesh[i, j], ky_kz_mesh[i, j]) for i, j in peak_indices])
            ax.plot(peak_coords[:, 0], peak_coords[:, 1], 'o', color='red', label='Peaks')

        # Threshold the intensities to create a binary image for Euler characteristic calculation
        binary_intensity = intensities_filled >= euler_binary_threshold

        # Compute the Euler characteristic
        euler_char = euler_number(binary_intensity)

        # Display the Euler characteristic on the plot
        ax.text(
            0.95, 0.95,
            f'Euler characteristic: {euler_char}',
            transform=ax.transAxes,
            fontsize=12,
            verticalalignment='top',
            horizontalalignment='right',
            bbox=dict(facecolor='white', alpha=0.5)
        )

        # Display binary points used in Euler characteristic calculation
        if display_euler_points:
            # Overlay the binary image onto the plot
            ax.contour(
                kx_mesh, ky_kz_mesh, binary_intensity,
                levels=[0.5], colors='white', linewidths=1.0, linestyles='--', label='Euler Binary'
            )

        # Add legend for peaks and Euler binary contour
        ax.legend(loc='lower left')

        if display_edges or display_components:
            # Apply Canny edge detection
            edges = canny(
                intensities_filled,
                sigma=sigma,
                low_threshold=low_threshold,
                high_threshold=high_threshold
            )

            if display_edges:
                # Overlay edges on the plot
                ax.contour(
                    kx_mesh, ky_kz_mesh, edges,
                    levels=[0.5], colors='cyan', linewidths=1.0
                )

            if display_components:
                # Label connected components
                labeled_array, num_features = label(edges, connectivity=2, return_num=True)

                # Iterate over each detected component
                for region_label in range(1, num_features + 1):
                    # Create a mask for the current component
                    component_mask = labeled_array == region_label

                    # Extract the coordinates of the component pixels
                    kx_component = kx_mesh[component_mask]
                    ky_kz_component = ky_kz_mesh[component_mask]

                    # Plot the component
                    ax.plot(
                        kx_component,
                        ky_kz_component,
                        '.', markersize=1, color='yellow'
                    )

        plt.tight_layout()
        plt.show()

    elif mode == 'E vs ky vs kx':
        # Handle 3D plot
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        colorbar = None  # Reset colorbar

        # Prepare lists to store kx, ky, E_binding, and intensity values
        kx_list = []
        ky_list = []
        E_binding_list = []
        intensity_list = []

        # Loop over all scans
        for i in range(len(data_proc)):
            df = data_proc[i]
            hv = data_attributes[i]['hv']
            polar_angle = data_attributes[i]['polar']
            polar_angle_rad = np.deg2rad(polar_angle)

            # Emission angles (theta) in degrees, convert to radians
            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)

            # Kinetic energy values
            E_kinetic_values = df.index.values.astype(float)

            # Compute E_binding_values
            E_binding_values = hv - work_function - E_kinetic_values

            # Compute k_magnitude
            k_magnitude = 0.5123 * np.sqrt(E_kinetic_values)  # Shape (n_Ekinetic,)

            # Compute kx_grid
            kx_grid = k_magnitude[:, np.newaxis] * np.sin(theta_rad[np.newaxis, :]) + x_offset  # Shape (n_Ekinetic, n_theta)

            # Compute ky_grid
            ky_grid = k_magnitude[:, np.newaxis] * np.sin(polar_angle_rad) + y_offset  # Shape (n_Ekinetic, 1)
            ky_grid = np.tile(ky_grid, (1, len(theta_rad)))  # Shape (n_Ekinetic, n_theta)

            # Compute E_binding_grid
            E_binding_grid = E_binding_values[:, np.newaxis]  # Shape (n_Ekinetic, 1)
            E_binding_grid = np.tile(E_binding_grid, (1, len(theta_rad)))  # Shape (n_Ekinetic, n_theta)

            # Intensities
            intensities = df.values  # Shape (n_Ekinetic, n_theta)

            # Apply moving average filter along energy axis
            if kernel_size > 1:
                intensities = np.apply_along_axis(
                    lambda m: moving_average(m, kernel_size),
                    axis=0,
                    arr=intensities
                )

            # Normalize intensities
            max_intensity = np.nanmax(intensities)
            if max_intensity > 0:
                intensities = intensities / max_intensity

            # Flatten the arrays
            kx_flat = kx_grid.flatten()
            ky_flat = ky_grid.flatten()
            E_binding_flat = E_binding_grid.flatten()
            intensities_flat = intensities.flatten()

            # Collect data
            kx_list.append(kx_flat)
            ky_list.append(ky_flat)
            E_binding_list.append(E_binding_flat)
            intensity_list.append(intensities_flat)

        # Concatenate the lists
        kx_array = np.concatenate(kx_list)
        ky_array = np.concatenate(ky_list)
        E_binding_array = np.concatenate(E_binding_list)
        intensity_array = np.concatenate(intensity_list)

        # Apply intensity threshold to remove points below threshold
        intensity_mask = intensity_array >= intensity_threshold
        kx_array = kx_array[intensity_mask]
        ky_array = ky_array[intensity_mask]
        E_binding_array = E_binding_array[intensity_mask]
        intensity_array = intensity_array[intensity_mask]

        # Check if there is data to plot
        if kx_array.size == 0:
            print("No data to plot with the given intensity threshold.")
            return

        # Normalize intensity_array for color mapping, but keep the colorbar range fixed
        # We use vmin=0 and vmax=1 to fix the colorbar range
        # Do not re-normalize intensity_array after applying the threshold
        max_intensity_global = 1.0  # Since intensities are already normalized to 1
        intensity_array_normalized = intensity_array / max_intensity_global

        if surface_plot:
            # For computational efficiency, bin the data before interpolation
            # Determine appropriate grid resolution based on data size
            num_data_points = kx_array.size
            max_grid_points = 100000  # Adjust as needed for performance
            grid_resolution = min(surface_resolution, int(np.sqrt(max_grid_points)))

            # Create grid for interpolation
            kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)
            ky_grid = np.linspace(ky_array.min(), ky_array.max(), grid_resolution)
            kx_mesh, ky_mesh = np.meshgrid(kx_grid, ky_grid)

            # Use LinearNDInterpolator for faster interpolation
            interpolator_E = LinearNDInterpolator(
                points=np.column_stack((kx_array, ky_array)),
                values=E_binding_array,
                fill_value=np.nan
            )
            E_binding_grid = interpolator_E(kx_mesh, ky_mesh)

            interpolator_I = LinearNDInterpolator(
                points=np.column_stack((kx_array, ky_array)),
                values=intensity_array_normalized,
                fill_value=0.0
            )
            intensity_grid = interpolator_I(kx_mesh, ky_mesh)

            # Mask points where intensity is below threshold or E_binding is NaN
            valid_mask = np.isfinite(E_binding_grid) & (intensity_grid >= intensity_threshold)
            E_binding_grid[~valid_mask] = np.nan
            intensity_grid[~valid_mask] = np.nan

            # Plot the surface
            surf = ax.plot_surface(
                kx_mesh, ky_mesh, E_binding_grid,
                facecolors=plt.cm.viridis(intensity_grid),
                linewidth=0, antialiased=False, shade=False
            )

            # Set axis labels and title
            ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
            ax.set_ylabel(r'$k_y$ (Å$^{-1}$)')
            ax.set_zlabel('Binding Energy (eV)')
            ax.set_title('3D Surface Plot: E vs $k_y$ vs $k_x$ with Intensity Coloring')

            # Adjust viewing angle for better visualization
            ax.view_init(elev=30, azim=225)

            # Add colorbar for intensity
            mappable = plt.cm.ScalarMappable(cmap='viridis', norm=plt.Normalize(vmin=0, vmax=1))
            mappable.set_array([])  # Dummy array for colorbar
            if colorbar is None:
                colorbar = fig.colorbar(mappable, ax=ax, label='Normalized Intensity')
            else:
                colorbar.update_normal(mappable)

            plt.tight_layout()
            plt.show()
        else:
            # Optimize for high number of points
            max_points = 100000  # Adjust as needed
            num_points = kx_array.size
            if num_points > max_points:
                # Randomly sample max_points indices
                indices = np.random.choice(num_points, max_points, replace=False)
                kx_array = kx_array[indices]
                ky_array = ky_array[indices]
                E_binding_array = E_binding_array[indices]
                intensity_array_normalized = intensity_array_normalized[indices]

            # Create a 3D scatter plot
            sc = ax.scatter(
                kx_array, ky_array, E_binding_array,
                c=intensity_array_normalized, cmap=rainbowlightct, marker='.', s=1, vmin=0, vmax=1
            )

            # Add colorbar
            mappable = plt.cm.ScalarMappable(cmap=rainbowlightct, norm=plt.Normalize(vmin=0, vmax=1))
            mappable.set_array([])  # Dummy array for colorbar
            if colorbar is None:
                colorbar = fig.colorbar(mappable, ax=ax, label='Normalized Intensity')
            else:
                colorbar.update_normal(mappable)

            ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
            ax.set_ylabel(r'$k_y$ (Å$^{-1}$)')
            ax.set_zlabel('Binding Energy (eV)')
            ax.set_title('3D Scatter Plot: E vs $k_y$ vs $k_x$')

            plt.tight_layout()
            plt.show()

    else:
        print("Invalid mode selected.")
        return

# Widgets for user interaction
mode_selector = widgets.Dropdown(
    options=['kx vs ky', 'kx vs kz', 'E vs kx', 'E vs ky vs kx'],
    value='kx vs ky',
    description='Select Mode:',
    style={'description_width': 'initial'}
)

# Create a slider for binding energy
E_binding_widget = widgets.FloatSlider(
    value=0.0,
    min=E_binding_min,
    max=E_binding_max,
    step=0.01,
    description='Binding Energy (eV):',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Add a toggle for enabling contour maps
contour_toggle = widgets.Checkbox(
    value=False,
    description='Enable Contour Map',
    style={'description_width': 'initial'}
)

# Add a slider to adjust contour density
contour_density_widget = widgets.IntSlider(
    value=20,
    min=1,
    max=100,
    step=1,
    description='Contour Density:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Widget for adjusting kernel size of moving average filter
kernel_size_widget = widgets.IntSlider(
    value=1,
    min=1,
    max=51,
    step=2,
    description='Kernel Size:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Widgets for adjusting x and y axis offsets
x_offset_widget = widgets.FloatSlider(
    value=0.0,
    min=-5.0,
    max=5.0,
    step=0.01,
    description='X Offset:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

y_offset_widget = widgets.FloatSlider(
    value=0.0,
    min=-5.0,
    max=5.0,
    step=0.01,
    description='Y Offset:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Intensity range is now between 0 and 1 after normalization
vmin_widget = widgets.FloatSlider(
    value=0.0,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Min Intensity:',
    continuous_update=False,
    readout_format='.2f',
    style={'description_width': 'initial'}
)

vmax_widget = widgets.FloatSlider(
    value=1.0,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Max Intensity:',
    continuous_update=False,
    readout_format='.2f',
    style={'description_width': 'initial'}
)

# Intensity threshold specific to the 3D mode
intensity_threshold_widget = widgets.FloatSlider(
    value=0.0,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Intensity Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Checkbox to enable surface plot
surface_plot_toggle = widgets.Checkbox(
    value=False,
    description='Enable Surface Plot',
    style={'description_width': 'initial'}
)

# Slider for surface resolution
surface_resolution_widget = widgets.IntSlider(
    value=200,
    min=50,
    max=1000,
    step=50,
    description='Surface Resolution:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Widgets for Canny edge detection parameters
sigma_widget = widgets.FloatSlider(
    value=1.0,
    min=0.1,
    max=5.0,
    step=0.1,
    description='Canny Sigma:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

low_threshold_widget = widgets.FloatSlider(
    value=0.1,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Canny Low Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

high_threshold_widget = widgets.FloatSlider(
    value=0.3,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Canny High Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Create a slider to select the scan number
scan_selector = widgets.IntSlider(
    value=0,
    min=0,
    max=len(data_proc) - 1,
    step=1,
    description='Scan Number:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Peak detection parameters
peak_threshold_widget = widgets.FloatSlider(
    value=0.5,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Peak Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

neighborhood_size_widget = widgets.IntSlider(
    value=5,
    min=1,
    max=21,
    step=2,
    description='Neighborhood Size:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

smoothing_sigma_widget = widgets.FloatSlider(
    value=1.0,
    min=0.0,
    max=5.0,
    step=0.1,
    description='Smoothing Sigma:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Euler characteristic calculation threshold
euler_binary_threshold_widget = widgets.FloatSlider(
    value=0.5,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Euler Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Checkbox to display Euler characteristic points
display_euler_points_toggle = widgets.Checkbox(
    value=False,
    description='Display Euler Points',
    style={'description_width': 'initial'}
)

# Toggle to display components and edges
display_edges_toggle = widgets.Checkbox(
    value=False,
    description='Display Edges',
    style={'description_width': 'initial'}
)

display_components_toggle = widgets.Checkbox(
    value=False,
    description='Display Components',
    style={'description_width': 'initial'}
)

# Organize widgets into a UI
ui = widgets.VBox([
    widgets.HBox([mode_selector, E_binding_widget, scan_selector]),
    widgets.HBox([vmin_widget, vmax_widget, intensity_threshold_widget, kernel_size_widget]),
    widgets.HBox([x_offset_widget, y_offset_widget]),
    widgets.HBox([sigma_widget, low_threshold_widget, high_threshold_widget]),
    widgets.HBox([peak_threshold_widget, neighborhood_size_widget, smoothing_sigma_widget]),
    widgets.HBox([euler_binary_threshold_widget, display_euler_points_toggle]),
    widgets.HBox([contour_toggle, contour_density_widget, surface_plot_toggle, surface_resolution_widget]),
    widgets.HBox([display_edges_toggle, display_components_toggle]),
])

# Update the interactive_output to include the new parameters
out = widgets.interactive_output(
    plot_constant_energy_map,
    {
        'mode': mode_selector,
        'E_binding': E_binding_widget,
        'vmin': vmin_widget,
        'vmax': vmax_widget,
        'use_contours': contour_toggle,
        'contour_levels': contour_density_widget,
        'kernel_size': kernel_size_widget,
        'x_offset': x_offset_widget,
        'y_offset': y_offset_widget,
        'sigma': sigma_widget,
        'low_threshold': low_threshold_widget,
        'high_threshold': high_threshold_widget,
        'display_edges': display_edges_toggle,
        'display_components': display_components_toggle,
        'scan_number': scan_selector,
        'peak_threshold': peak_threshold_widget,
        'neighborhood_size': neighborhood_size_widget,
        'smoothing_sigma': smoothing_sigma_widget,
        'euler_binary_threshold': euler_binary_threshold_widget,
        'display_euler_points': display_euler_points_toggle,
        'intensity_threshold': intensity_threshold_widget,
        'surface_plot': surface_plot_toggle,
        'surface_resolution': surface_resolution_widget,
    }
)

display(ui, out)

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata, LinearNDInterpolator
from scipy.ndimage import gaussian_filter, maximum_filter
from skimage.measure import label, euler_number
from skimage.feature import canny
from mpl_toolkits.mplot3d import Axes3D
import ipywidgets as widgets
from IPython.display import display
from matplotlib.colors import ListedColormap

# Momentum space conversion with vectorized NumPy operations
def compute_momentum_coordinates(df, hv, polar_angle):
    emission_angles = df.columns.values.astype(float)
    theta_rad = np.deg2rad(emission_angles)
    E_kinetic_values = df.index.values.astype(float)
    
    # Convert to NumPy arrays for vectorized operations
    E_kinetic_grid = E_kinetic_values[:, None]
    theta_grid = theta_rad[None, :]
    
    # Compute momentum components
    k_mag = 0.5123 * np.sqrt(E_kinetic_grid)
    kx = k_mag * np.sin(theta_grid) * np.cos(np.deg2rad(polar_angle))
    ky = k_mag * np.sin(theta_grid) * np.sin(np.deg2rad(polar_angle))
    kz = 0.5123 * np.sqrt(E_kinetic_grid * np.cos(theta_grid)**2 + V0)
    
    return kx, ky, kz, hv - work_function - E_kinetic_grid

# Morse theory implementation
def find_critical_points(intensity_grid, sigma=1.0):
    """Identifies maxima, minima, and saddle points using Hessian analysis"""
    smoothed = gaussian_filter(intensity_grid, sigma=sigma)
    dy, dx = np.gradient(smoothed)
    dyy, dxy = np.gradient(dy)
    dxx, _ = np.gradient(dx)
    
    hessian_det = dxx * dyy - dxy**2
    maxima = (dxx < 0) & (dyy < 0) & (hessian_det > 0)
    minima = (dxx > 0) & (dyy > 0) & (hessian_det > 0)
    saddles = hessian_det < 0
    
    return maxima, minima, saddles

def compute_euler_characteristic(maxima, minima, saddles):
    n_max = np.sum(maxima)
    n_min = np.sum(minima)
    n_sad = np.sum(saddles)
    return n_max - n_min + n_sad

# Enhanced 3D visualization with Morse analysis
def interactive_3d_analysis(intensity_threshold=0.3, grid_resolution=100, sigma=1.0):
    # Threshold data
    mask = intensity_array > intensity_threshold
    kx_thresh = kx_array[mask]
    ky_thresh = ky_array[mask]
    E_thresh = E_binding_array[mask]
    
    # Grid interpolation
    grid_x, grid_y = np.mgrid[
        kx_array.min():kx_array.max():grid_resolution*1j,
        ky_array.min():ky_array.max():grid_resolution*1j
    ]
    grid_z = griddata(
        (kx_thresh, ky_thresh), E_thresh, (grid_x, grid_y),
        method='cubic', fill_value=np.nan
    )
    
    # Morse theory analysis
    intensity_grid = griddata(
        (kx_thresh, ky_thresh), intensity_array[mask], (grid_x, grid_y),
        method='linear', fill_value=0
    )
    maxima, minima, saddles = find_critical_points(intensity_grid, sigma)
    χ = compute_euler_characteristic(maxima, minima, saddles)
    
    # Plotting
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    surf = ax.plot_surface(grid_x, grid_y, grid_z, cmap='viridis',
                          rstride=2, cstride=2, alpha=0.8)
    ax.scatter(kx_thresh, ky_thresh, E_thresh, c=intensity_array[mask],
              cmap='hot', s=1, alpha=0.5)
    ax.set_title(f'Euler Characteristic: {χ}')
    plt.colorbar(surf, label='Interpolated Energy')
    plt.show()

# Widgets for interactive analysis
# For FloatSlider
controls = widgets.interactive(
    interactive_3d_analysis,
    intensity_threshold=widgets.FloatSlider(
        value=0.1,
        min=0,
        max=1,
        step=0.05,
        description='Intensity Threshold'
    ),
    grid_resolution=widgets.IntSlider(
        value=100,
        min=50,
        max=500,
        step=50,
        description='Grid Resolution'
    ),
    sigma=widgets.FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Smoothing Sigma'
    )
)


display(controls)

import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import igor.binarywave as ibw

def load_ibw_file():
    file_path = filedialog.askopenfilename(
        title="Select an ARPES IBW File",
        filetypes=[("IBW Files", "*.ibw")]
    )
    if not file_path:
        return None, None
    
    try:
        ibw_data = ibw.load(file_path)
        wave = ibw_data['wave']
        waveheader = wave.get('waveheader', {})
        
        # Extract data array
        data_array = wave['wData']
        if data_array.ndim != 2:
            data_array = np.squeeze(data_array)
            if data_array.ndim != 2:
                data_array = data_array[0]

        # Get coordinates
        energy_vals = waveheader.get('sfA', np.linspace(0, 10, data_array.shape[0]))
        angle_vals = waveheader.get('sfB', np.linspace(-5, 5, data_array.shape[1]))
        print(data_array.shape)
        
        
        # Calculate binding energy
        work_function = 4.5
        hv = float(waveheader.get('hv', 21.2))
        binding_energy = hv - work_function - np.abs(energy_vals)
        
        # Create DataFrame
        df = pd.DataFrame(
            data_array,
            columns=angle_vals.astype(float),
            index=binding_energy.astype(float)
        )
        
        # Store attributes
        attrs = {
            'hv': hv,
            'polar': waveheader.get('polar', 0.0)
        }
        
        return [df], [attrs]

    except Exception as e:
        messagebox.showerror("Error", f"Failed to load file:\n{str(e)}")
        return None, None

# GUI setup
root = tk.Tk()
root.title("ARPES IBW File Loader")

# Initialize global variables
data = []
data_attributes = []

def load_and_store_data():
    global data, data_attributes
    new_data, new_attrs = load_ibw_file()
    
    if new_data and new_attrs:
        data.extend(new_data)
        data_attributes.extend(new_attrs)
        messagebox.showinfo("Success", f"Loaded IBW file\nTotal files loaded: {len(data)}")

load_button = tk.Button(root, text="Load IBW File", command=load_and_store_data)
load_button.pack(pady=20)

root.mainloop()

data_proc = data.copy()
for i in range(len(data_proc)):
    new_index = data_attributes[i]['hv'] - work_function - np.abs(data[i].index)
    data_proc[i].index = new_index

data_proc


import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator

# Physics constants 
work_function = 4.5  # Material-specific parameter
h = 4.135667696e-15  # Planck's constant (eV·s)
c = 299792458        # Speed of light (m/s)
m_e = 0.51099895e6   # Electron mass (eV/c²)
a0 = 0.529177e-10    # Bohr radius (m)

def generate_arpes_map(data_proc, data_attributes, energy_val):
    """Plot ARPES constant energy map in momentum space"""
    
    # Validate input structure
    if not data_proc or not data_attributes:
        raise ValueError("No valid ARPES data loaded")
    
    # Select energy slice nearest to target value
    energy_idx = np.abs(data_proc[0].index - energy_val).argmin()
    selected_energy = data_proc[0].index[energy_idx]
    
    # Extract experimental parameters from first scan
    hv = data_attributes[0]['hv']
    polar = np.deg2rad(data_attributes[0]['polar'])  # Polar angle (θ)
    
    # Calculate momentum conversion factors
    k_scale = np.sqrt(2*m_e/(h**2*(2*np.pi)**2)) * 1e-10  # Å⁻¹ conversion
    kinetic_energy = hv - work_function - selected_energy
    k0 = k_scale * np.sqrt(kinetic_energy)
    
    # Convert angular coordinates to momentum components
    angle_vals = np.deg2rad(data_proc[0].columns.values)  # Angular scan values (ϕ)
    kx = k0 * np.sin(polar + angle_vals)
    ky = k0 * np.cos(polar + angle_vals) - k0 * np.cos(polar)
    
    # Create momentum grid
    intensity = data_proc[0].iloc[energy_idx].values
    k_mesh = np.array(np.meshgrid(kx, ky))
    
    # Quantum state density calculation (example)
    dos = intensity / (k0**2 * np.abs(np.sin(angle_vals + polar)) + 1e-6)
    
    # Visualize data
    plt.figure(figsize=(10, 8), dpi=300)
    ax = plt.gca()
    
    cmap = plt.cm.viridis.copy()
    cmap.set_bad(color='#2B2D42')  # Handle NaN values
    
    im = ax.pcolormesh(kx, ky, intensity.reshape(len(ky), len(kx)).T,
                      cmap=cmap, shading='gouraud', 
                      vmax=np.percentile(intensity, 99.5),
                      rasterized=True)
    
    # Fermi surface annotations
    ax.contour(kx, ky, dos.reshape(len(ky), len(kx)).T, 
              levels=5, colors='#F7E3AF', linewidths=0.75)
    
    # Format plot
    ax.xaxis.set_minor_locator(MultipleLocator(0.1))
    ax.yaxis.set_minor_locator(MultipleLocator(0.1))
    ax.set_xlabel(r'$k_x$ (Å⁻¹)', fontsize=12, labelpad=10)
    ax.set_ylabel(r'$k_y$ (Å⁻¹)', fontsize=12, labelpad=10)
    ax.set_title(f'ARPES Constant Energy Map: {selected_energy:.2f} eV',
                fontsize=14, pad=15)
    
    cbar = plt.colorbar(im, ax=ax, pad=0.02)
    cbar.set_label('Photoemission Intensity (a.u.)', 
                  rotation=270, labelpad=20)
    
    # Brillouin zone overlay (example for simple cubic)
    lattice_constant = 4.0  # System-dependent parameter
    bz_edge = np.pi / lattice_constant
    ax.plot([-bz_edge, bz_edge, bz_edge, -bz_edge, -bz_edge],
           [-bz_edge, -bz_edge, bz_edge, bz_edge, -bz_edge],
           color='#E76F51', linestyle='--', linewidth=1.5, alpha=0.8)
    
    plt.tight_layout()
    plt.show()

# Usage example
generate_arpes_map(data_proc, data_attributes, energy_val=-0.15)


import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata, LinearNDInterpolator
import ipywidgets as widgets
from IPython.display import display
from matplotlib.colors import ListedColormap
from scipy.ndimage import gaussian_filter
from mpl_toolkits.mplot3d import Axes3D



# Constants
work_function = 4.5  # eV
V0 = 10  # Inner potential in eV

data_proc = data.copy()
for i in range(len(data_proc)):
    new_index = data_attributes[i]['hv'] - work_function - np.abs(data[i].index)
    data_proc[i].index = new_index

# Calculate binding energy range from all scans
E_binding_values = []
for i in range(len(data_proc)):
    df = data_proc[i]
    hv = data_attributes[i]['hv']
    E_kinetic = df.index.values.astype(float)
    E_binding = hv - work_function - E_kinetic
    E_binding_values.extend(E_binding)

E_binding_min = np.nanmin(E_binding_values)
E_binding_max = np.nanmax(E_binding_values)

# Function to apply moving average filter
def moving_average(data, kernel_size):
    kernel = np.ones(kernel_size) / kernel_size
    return np.convolve(data, kernel, mode='same')


# Function to apply moving average filter
def moving_average(data, kernel_size):
    kernel = np.ones(kernel_size) / kernel_size
    return np.convolve(data, kernel, mode='same')

def plot_constant_energy_map(
    mode, E_binding, vmin, vmax, use_contours, contour_levels,
    kernel_size, x_offset, y_offset, sigma, low_threshold, high_threshold,
    display_edges, display_components, scan_number
):
    global pcm, cs, colorbar, fig, ax

    plt.clf()

    if mode == 'E vs kx':
        fig, ax = plt.subplots(figsize=(10, 8))
        colorbar = None

        if scan_number < 0 or scan_number >= len(data_proc):
            print("Invalid scan number selected.")
            return

        df = data_proc[scan_number]
        hv = data_attributes[scan_number]['hv']

        emission_angles = df.columns.values.astype(float)
        theta_rad = np.deg2rad(emission_angles)
        E_kinetic_values = df.index.values.astype(float)

        if np.any(np.diff(E_kinetic_values) < 0):
            E_kinetic_values = E_kinetic_values[::-1]
            df = df.iloc[::-1]

        theta_grid, E_kinetic_grid = np.meshgrid(theta_rad, E_kinetic_values, indexing='xy')
        E_binding_grid = hv - work_function - E_kinetic_grid + y_offset
        kx_grid = 0.5123 * np.sqrt(E_kinetic_grid) * np.sin(theta_grid) + x_offset
        intensities = df.values

        if kernel_size > 1:
            intensities = np.apply_along_axis(
                lambda m: moving_average(m, kernel_size),
                axis=0,
                arr=intensities
            )

        max_intensity = np.nanmax(intensities)
        if max_intensity > 0:
            intensities = intensities / max_intensity

        valid_mask = np.isfinite(kx_grid) & np.isfinite(E_binding_grid) & np.isfinite(intensities)
        intensities[~valid_mask] = np.nan

        pcm = ax.pcolormesh(
            kx_grid, E_binding_grid, intensities,
            shading='auto', cmap=rainbowlightct, vmin=vmin, vmax=vmax
        )

        if colorbar is None:
            colorbar = fig.colorbar(pcm, ax=ax, label='Normalized Intensity')
        else:
            colorbar.update_normal(pcm)

        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
        ax.set_ylabel('Binding Energy (eV)')
        ax.set_title(f'Binding Energy vs $k_x$ Map\nScan Number: {scan_number}')
        ax.set_ylim(ax.get_ylim()[::-1])
        plt.tight_layout()
        plt.show()

    elif mode in ['kx vs ky', 'kx vs kz']:
        fig, ax = plt.subplots(figsize=(10, 8))
        colorbar = None

        kx_list = []
        ky_kz_list = []
        intensity_list = []

        for i in range(len(data_proc)):
            df = data_proc[i]
            hv = data_attributes[i]['hv']
            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)
            E_kinetic = hv - work_function - E_binding

            if E_kinetic < df.index.values.min() or E_kinetic > df.index.values.max():
                continue

            k_magnitude = 0.5123 * np.sqrt(E_kinetic)
            kx = k_magnitude * np.sin(theta_rad) + x_offset

            if mode == 'kx vs ky':
                polar_angle_rad = np.deg2rad(data_attributes[i]['polar'])
                ky = k_magnitude * np.sin(polar_angle_rad) + y_offset
                ky_kz_array = np.full_like(kx, ky)
                ylabel = r'$k_y$ (Å$^{-1}$)'
                title = f'Constant Energy Map at Binding Energy = {E_binding:.2f} eV\nMode: kx vs ky'
            else:
                kz = 0.5123 * np.sqrt(E_kinetic * np.cos(theta_rad)**2 + V0) + y_offset
                ky_kz_array = kz
                ylabel = r'$k_z$ (Å$^{-1}$)'
                title = f'Constant Energy Map at Binding Energy = {E_binding:.2f} eV\nMode: kx vs kz'

            kx_list.extend(kx)
            ky_kz_list.extend(ky_kz_array)
            intensity_list.extend(df.loc[E_kinetic].values if E_kinetic in df.index else 
                                df.apply(lambda col: np.interp(E_kinetic, df.index, col)).values)

        kx_array = np.array(kx_list)
        ky_kz_array = np.array(ky_kz_list)
        intensity_array = np.array(intensity_list)

        if kx_array.size == 0:
            print("No data available for the selected binding energy.")
            return

        grid_resolution = 600
        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)
        ky_kz_grid = np.linspace(ky_kz_array.min(), ky_kz_array.max(), grid_resolution)
        kx_mesh, ky_kz_mesh = np.meshgrid(kx_grid, ky_kz_grid)

        intensity_grid = griddata(
            (kx_array, ky_kz_array), intensity_array, (kx_mesh, ky_kz_mesh), method='cubic'
        )
        intensity_grid = np.nan_to_num(intensity_grid)

        max_intensity = intensity_grid.max()
        if max_intensity > 0:
            intensity_grid /= max_intensity

        if use_contours:
            cs = ax.contour(kx_mesh, ky_kz_mesh, intensity_grid, levels=contour_levels, cmap=rainbowlightct)
            if colorbar is None:
                colorbar = fig.colorbar(cs, ax=ax, label='Intensity')
            else:
                colorbar.update_normal(cs)
        else:
            pcm = ax.pcolormesh(kx_mesh, ky_kz_mesh, intensity_grid, shading='auto', cmap=rainbowlightct, vmin=vmin, vmax=vmax)
            if colorbar is None:
                colorbar = fig.colorbar(pcm, ax=ax, label='Intensity')
            else:
                colorbar.update_normal(pcm)

        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
        ax.set_ylabel(ylabel)
        ax.set_title(title)
        plt.tight_layout()
        plt.show()

# Widget setup
# Widget setup
mode_selector = widgets.Dropdown(
    options=['kx vs ky', 'kx vs kz', 'E vs kx'],
    value='kx vs ky',
    description='Select Mode:',
    style={'description_width': 'initial'}
)

E_binding_widget = widgets.FloatSlider(
    value=0.0,
    min=E_binding_min,
    max=E_binding_max,
    step=0.01,
    description='Binding Energy (eV):',
    continuous_update=False,
    style={'description_width': 'initial'}
)


contour_toggle = widgets.Checkbox(
    value=False,
    description='Enable Contour Map',
    style={'description_width': 'initial'}
)

contour_density_widget = widgets.IntSlider(
    value=20,
    min=1,
    max=100,
    step=1,
    description='Contour Density:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

kernel_size_widget = widgets.IntSlider(
    value=1,
    min=1,
    max=51,
    step=2,
    description='Kernel Size:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

x_offset_widget = widgets.FloatSlider(
    value=0.0,
    min=-5.0,
    max=5.0,
    step=0.01,
    description='X Offset:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

y_offset_widget = widgets.FloatSlider(
    value=0.0,
    min=-5.0,
    max=5.0,
    step=0.01,
    description='Y Offset:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

vmin_widget = widgets.FloatSlider(
    value=0.0,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Min Intensity:',
    continuous_update=False,
    readout_format='.2f',
    style={'description_width': 'initial'}
)

vmax_widget = widgets.FloatSlider(
    value=1.0,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Max Intensity:',
    continuous_update=False,
    readout_format='.2f',
    style={'description_width': 'initial'}
)

scan_selector = widgets.IntSlider(
    value=0,
    min=0,
    max=len(data_proc) - 1,
    step=1,
    description='Scan Number:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Add this widget definition before the UI setup
sigma_widget = widgets.FloatSlider(
    value=1.0,
    min=0.1,
    max=5.0,
    step=0.1,
    description='Gaussian Sigma:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

low_threshold_widget = widgets.FloatSlider(
    value=0.1,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Low Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

high_threshold_widget = widgets.FloatSlider(
    value=0.9,
    min=0.0,
    max=1.0,
    step=0.01,
    description='High Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

display_edges_toggle = widgets.Checkbox(
    value=False,
    description='Show Edges',
    style={'description_width': 'initial'}
)

display_components_toggle = widgets.Checkbox(
    value=False,
    description='Show Components',
    style={'description_width': 'initial'}
)

# Update UI layout to include missing widgets
ui = widgets.VBox([
    widgets.HBox([mode_selector, E_binding_widget, scan_selector]),
    widgets.HBox([vmin_widget, vmax_widget, kernel_size_widget]),
    widgets.HBox([x_offset_widget, y_offset_widget, sigma_widget]),
    widgets.HBox([low_threshold_widget, high_threshold_widget]),
    widgets.HBox([contour_toggle, contour_density_widget]),
    widgets.HBox([display_edges_toggle, display_components_toggle])
])




out = widgets.interactive_output(
    plot_constant_energy_map,
    {
        'mode': mode_selector,
        'E_binding': E_binding_widget,
        'vmin': vmin_widget,
        'vmax': vmax_widget,
        'use_contours': contour_toggle,
        'contour_levels': contour_density_widget,
        'kernel_size': kernel_size_widget,
        'x_offset': x_offset_widget,
        'y_offset': y_offset_widget,
        'sigma': sigma_widget,
        'low_threshold': low_threshold_widget,
        'high_threshold': high_threshold_widget,
        'display_edges': display_edges_toggle,
        'display_components': display_components_toggle,
        'scan_number': scan_selector
    }
)

display(ui, out)


data_proc

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import filedialog, messagebox
import igor.binarywave as ibw  # make sure you have a module to load IBW files

def load_ibw_file():
    # Open a dialog for a single IBW file
    file_path = filedialog.askopenfilename(
        title="Select a Live Polar IBW File",
        filetypes=[("IBW Files", "*.ibw")]
    )
    if not file_path:
        return None
    try:
        ibw_data = ibw.load(file_path)
    except Exception as e:
        messagebox.showerror("Error", f"Failed to load file:\n{str(e)}")
        return None
    return ibw_data

# Initialize and hide the tkinter window
root = tk.Tk()
root.withdraw()

# Load the IBW file
ibw_data = load_ibw_file()
if ibw_data is None:
    exit()

# -----------------------------------------------------------------
# Extract wave data and coordinate information
#
# We assume:
#  • The 2D intensity array is stored in ibw_data['wave']['wData'].
#  • The kinetic energy axis is in waveheader key 'sfA'.
#  • The emission angle data (in degrees) is in waveheader key 'sfB'.
#
# If these keys are missing, reasonable default coordinate arrays are generated.
# -----------------------------------------------------------------
try:
    data_array = ibw_data['wave']['wData']
except KeyError:
    messagebox.showerror("Error", "The selected IBW file does not contain a valid 'wData' array")
    exit()

# Remove any redundant dimensions to ensure a 2D array
if data_array.ndim != 2:
    data_array = np.squeeze(data_array)
    if data_array.ndim != 2:
        data_array = data_array[0]  # try selecting a 2D slice
        if data_array.ndim != 2:
            messagebox.showerror("Error", f"Data array has shape {data_array.shape} and cannot be converted to 2D.")
            exit()

# Retrieve coordinate arrays from the header (or generate defaults)
waveheader = ibw_data['wave'].get('waveheader', {})
energy_vals = waveheader.get('sfA', None)  # kinetic energies
angle_vals = waveheader.get('sfB', None)   # emission angles in degrees

if energy_vals is None or angle_vals is None:
    n_rows, n_cols = data_array.shape
    energy_vals = np.linspace(0, 10, n_rows)     # Example: kinetic energy in eV
    angle_vals = np.linspace(-5, 5, n_cols)        # Example: emission angles in degrees
else:
    energy_vals = np.array(energy_vals)
    angle_vals = np.array(angle_vals)

# -----------------------------------------------------------------
# Metadata and coordinate conversion to binding energy
#
# The binding energy is computed as:
#   E_binding = hv - work_function - |E_kinetic|
# where hv is the photon energy and work_function is a known constant.
# -----------------------------------------------------------------
work_function = 4.5  # in eV
hv = float(waveheader.get('hv', 21.2))  # Example default photon energy in eV

binding_energy = hv - work_function - np.abs(energy_vals)

# Build a pandas DataFrame with binding energies as the index (rows) and
# emission angles (in degrees) as the columns.
df = pd.DataFrame(data_array, columns=angle_vals, index=binding_energy)

# -----------------------------------------------------------------
# Plot the constant energy map using angles instead of k-space conversion.
#
# The x-axis now displays the emission angle directly.
# -----------------------------------------------------------------
plt.figure(figsize=(8, 6))
plt.pcolormesh(angle_vals, binding_energy, df.values, shading='auto', cmap='viridis')
plt.xlabel("Emission Angle (°)")
plt.ylabel("Binding Energy (eV)")
plt.title("Constant Energy Map (Angles Only)")
plt.gca().invert_yaxis()  # Binding energy is typically increasing downward
plt.colorbar(label="Intensity")
plt.tight_layout()
plt.show()


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import filedialog, messagebox
import igor.binarywave as ibw

def load_ibw_file():
    file_path = filedialog.askopenfilename(
        title="Select Live Polar IBW File",
        filetypes=[("IBW Files", "*.ibw")]
    )
    if not file_path:
        return None
    try:
        return ibw.load(file_path)
    except Exception as e:
        messagebox.showerror("Error", f"File load failed:\n{str(e)}")
        return None

# GUI setup
root = tk.Tk()
root.withdraw()

# Load IBW data
ibw_data = load_ibw_file()
if ibw_data is None:
    exit()

# Extract and validate data array
try:
    data_array = ibw_data['wave']['wData']
    # Remove singleton dimensions and ensure 2D
    data_array = np.squeeze(data_array)
    #print(data_array)
    if data_array.ndim != 2:
        raise ValueError("Data array must be 2D after squeezing")
except KeyError:
    messagebox.showerror("Error", "Missing 'wData' array in IBW file")
    exit()

# Coordinate extraction
waveheader = ibw_data['wave'].get('waveheader', {})
energy_vals = waveheader.get('sfA', None)
phi_vals = waveheader.get('sfB', None)  # Angular coordinates in degrees

# Generate default coordinates if missing
if energy_vals is None or phi_vals is None:
    n_rows, n_cols = data_array.shape
    energy_vals = np.linspace(0, 10, n_rows)  # Kinetic energy (eV)
    phi_vals = np.linspace(-30, 30, n_cols)   # Emission angles (degrees)

energy_vals = np.abs(np.asarray(energy_vals))
phi_vals = np.asarray(phi_vals)

# Binding energy calculation
work_function = 4.5  # eV
hv = float(waveheader.get('hv', 21.2))  # Photon energy
binding_energy = hv - work_function - energy_vals

# Create DataFrame with angular coordinates
df = pd.DataFrame(data_array, 
                 columns=phi_vals, 
                 index=binding_energy)

print(df)

df


import numpy as np
from tkinter import filedialog, messagebox
import igor.binarywave as ibw

def load_ibw_file_to_array():
    """
    Load data from an IBW file into a numpy array.
    
    Returns:
    - A numpy array containing the data if loaded successfully.
    - None if the file is not selected or if loading fails.
    """
    file_path = filedialog.askopenfilename(
        title="Select Live Polar IBW File",
        filetypes=[("IBW Files", "*.ibw")]
    )
    if not file_path:
        return None
    try:
        data = ibw.load(file_path)
        
        # Assuming 'data' contains 'wave' and 'note' as per igor.binarywave load function
        # Extract the actual numeric data if needed
        if 'wave' in data:
            wave_data = data['wave']
            # Convert to numpy array if not already
            if not isinstance(wave_data, np.ndarray):
                numpy_array = np.array(wave_data)
            else:
                numpy_array = wave_data
            return numpy_array
        else:
            messagebox.showerror("Error", f"Loaded data does not contain expected 'wave' key.")
            return None
    except Exception as e:
        messagebox.showerror("Error", f"File load failed:\n{str(e)}")
        return None

# Example usage
if __name__ == "__main__":
    array_data = load_ibw_file_to_array()
    if array_data is not None:
        print("Loaded Data as Numpy Array:")
        #print(array_data)

        print(np.shape(array_data))

np.shape(array_data)

import numpy as np

def load_ibw_file(filename):
    """
    Load data from an ASCII .ibw file into a numpy array.
    
    Parameters:
    - filename: Path to the .ibw file.
    
    Returns:
    - A numpy array containing the data.
    """
    try:
        # Attempt to load the file directly as text
        # The following assumes each row has the same number of columns.
        # Adjust this based on your file format.
        data = np.loadtxt(filename)
        return data
    except Exception as e:
        print(f"Failed to load {filename}: {e}")
        return None

# Example usage
filename = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/LP1_75LH_AZm75.ibw'
data = load_ibw_file(filename)

if data is not None:
    print("Loaded Data:")
    print(data)


