#!/bin/bash
# ARPES Analysis GUI Launcher for Linux/macOS
# This script activates the pyarpesenv environment and runs the GUI

echo "🔬 ARPES Analysis GUI Launcher"
echo "================================"

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Conda not found in PATH"
    echo "Please make sure Anaconda/Miniconda is installed and in your PATH"
    echo "You may need to run: source ~/anaconda3/etc/profile.d/conda.sh"
    read -p "Press Enter to exit..."
    exit 1
fi

# Initialize conda for bash
eval "$(conda shell.bash hook)"

echo "🐍 Activating pyarpesenv environment..."
conda activate pyarpesenv

if [ $? -ne 0 ]; then
    echo "❌ Failed to activate pyarpesenv environment"
    echo "Please make sure the environment exists:"
    echo "   conda env list"
    echo "If it doesn't exist, create it first or check the environment name"
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✅ Environment activated successfully"
echo "🚀 Launching ARPES Analysis GUI..."

# Run the Python launcher
python launch_arpes_gui.py

# Check exit status
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ GUI exited with error"
    read -p "Press Enter to exit..."
else
    echo ""
    echo "👋 GUI closed successfully"
fi
