@echo off
REM ARPES Analysis GUI Launcher for Windows
REM This script activates the pyarpesenv environment and runs the GUI

echo 🔬 ARPES Analysis GUI Launcher
echo ================================

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Conda not found in PATH
    echo Please make sure Anaconda/Miniconda is installed and in your PATH
    pause
    exit /b 1
)

echo 🐍 Activating pyarpesenv environment...
call conda activate pyarpesenv
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to activate pyarpesenv environment
    echo Please make sure the environment exists:
    echo    conda env list
    echo If it doesn't exist, create it first or check the environment name
    pause
    exit /b 1
)

echo ✅ Environment activated successfully
echo 🚀 Launching ARPES Analysis GUI...

REM Run the Python launcher
python launch_arpes_gui.py

REM Keep window open if there was an error
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ GUI exited with error
    pause
)

echo.
echo 👋 GUI closed successfully
pause
